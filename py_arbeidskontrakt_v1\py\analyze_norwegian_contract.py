"""
Norwegian Employment Contract Analyzer

Specifically analyzes the Norwegian employment contract PDF and generates
a customized recreation for Ringerike Landskap AS.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

from pdf_analyzer import PDFAnalyzer


def analyze_norwegian_contract():
    """Analyze the Norwegian employment contract PDF."""
    
    # Path to the Norwegian contract PDF
    contract_path = Path("../Norwegian - bokmal - Standard contract of employment.pdf")
    
    if not contract_path.exists():
        print(f"Contract file not found: {contract_path}")
        print("Please ensure the Norwegian contract PDF is in the parent directory.")
        return
    
    print("Analyzing Norwegian Employment Contract...")
    print(f"File: {contract_path}")
    
    try:
        # Create analyzer
        analyzer = PDFAnalyzer(str(contract_path))
        
        # Perform analysis
        results = analyzer.analyze_complete()
        
        # Display results
        print(f"\n=== Analysis Results ===")
        file_info = results.get('file_info', {})
        print(f"Pages: {file_info.get('num_pages', 'unknown')}")
        print(f"Encrypted: {file_info.get('encrypted', False)}")
        
        form_fields = results.get('form_fields', [])
        print(f"Form fields found: {len(form_fields)}")
        
        if form_fields:
            print(f"\n=== Form Fields ===")
            for i, field in enumerate(form_fields):
                print(f"{i+1}. {field.get('name', 'unnamed')} ({field.get('type', 'unknown')})")
                rect = field.get('rect', [])
                if len(rect) >= 4:
                    print(f"   Position: x={rect[0]:.1f}, y={rect[1]:.1f}, w={rect[2]-rect[0]:.1f}, h={rect[3]-rect[1]:.1f}")
                if field.get('value'):
                    print(f"   Default value: {field['value']}")
        
        # Extract text content
        text_content = results.get('text_content', {})
        if text_content.get('full_text'):
            print(f"\n=== Text Content Sample ===")
            sample_text = text_content['full_text'][:500]
            print(sample_text)
            if len(text_content['full_text']) > 500:
                print("... (truncated)")
        
        # Save detailed analysis
        analyzer.save_analysis("norwegian_contract_analysis.json")
        
        # Generate custom recreation code
        generate_custom_code(results)
        
        print(f"\n=== Generated Files ===")
        print("- norwegian_contract_analysis.json (detailed analysis)")
        print("- recreate_norwegian_contract.py (custom recreation code)")
        
    except Exception as e:
        print(f"Error analyzing contract: {e}")
        import traceback
        traceback.print_exc()


def generate_custom_code(analysis_results):
    """Generate custom code for recreating the Norwegian contract."""
    
    form_fields = analysis_results.get('form_fields', [])
    text_content = analysis_results.get('text_content', {})
    
    code_lines = [
        '"""',
        'Norwegian Employment Contract Recreation',
        'Customized for Ringerike Landskap AS',
        '"""',
        '',
        'import sys',
        'from pathlib import Path',
        '',
        '# Add parent directory to path for imports',
        'sys.path.append(str(Path(__file__).parent))',
        '',
        'from pdf_engine import PDFEngine',
        '',
        '',
        'def create_norwegian_contract():',
        '    """Create a Norwegian employment contract based on the official template."""',
        '    ',
        '    # Initialize the PDF engine',
        '    engine = PDFEngine()',
        '    '
    ]
    
    if form_fields:
        # Interactive form version
        code_lines.extend([
            '    # Create interactive form based on Norwegian template',
            '    form = engine.create_form("norwegian_contract_interactive.pdf")',
            '    ',
            '    # Add form fields based on analysis',
        ])
        
        # Group fields by type for better organization
        text_fields = [f for f in form_fields if f.get('type') == 'text']
        checkboxes = [f for f in form_fields if f.get('type') in ['button', 'checkbox']]
        signatures = [f for f in form_fields if f.get('type') == 'signature']
        
        if text_fields:
            code_lines.append('    # Text input fields')
            for field in text_fields:
                field_code = generate_field_code_custom(field)
                code_lines.append(f'    {field_code}')
            code_lines.append('    ')
        
        if checkboxes:
            code_lines.append('    # Checkboxes and buttons')
            for field in checkboxes:
                field_code = generate_field_code_custom(field)
                code_lines.append(f'    {field_code}')
            code_lines.append('    ')
        
        if signatures:
            code_lines.append('    # Signature fields')
            for field in signatures:
                field_code = generate_field_code_custom(field)
                code_lines.append(f'    {field_code}')
            code_lines.append('    ')
        
        code_lines.extend([
            '    # Render the interactive form',
            '    form.render_to_file("norwegian_contract_interactive.pdf")',
            '    print("Interactive Norwegian contract created: norwegian_contract_interactive.pdf")',
            '    ',
            '    # Also create a static version with Ringerike Landskap details',
            '    create_static_version(engine)',
        ])
    
    else:
        # Static document version only
        code_lines.extend([
            '    # Create static document version',
            '    create_static_version(engine)',
        ])
    
    # Add static version function
    code_lines.extend([
        '',
        '',
        'def create_static_version(engine):',
        '    """Create a static version pre-filled for Ringerike Landskap AS."""',
        '    ',
        '    doc = engine.create_document("norwegian_contract_ringerike.pdf")',
        '    ',
        '    # Add contract header',
        '    doc.add_title("ARBEIDSAVTALE") \\',
        '       .add_spacer(10) \\',
        '       .add_paragraph("Arbeidsgiver: Ringerike Landskap AS") \\',
        '       .add_paragraph("Organisasjonsnummer: 924 826 541") \\',
        '       .add_paragraph("Adresse: c/o Kim Tuvsjøen, Birchs vei 7, 3530 Røyse") \\',
        '       .add_spacer(15)',
        '    ',
        '    # Employee information section',
        '    doc.add_paragraph("Arbeidstaker: ________________________________") \\',
        '       .add_paragraph("Fødselsnummer: ________________________________") \\',
        '       .add_paragraph("Adresse: ________________________________") \\',
        '       .add_spacer(20)',
        '    ',
        '    # Contract sections based on Norwegian requirements',
        '    sections = [',
        '        ("1. ARBEIDSSTED", ',
        '         "Arbeidstakeren skal arbeide ved Ringerike Landskap AS med hovedarbeidssted i Røyse kommune. "',
        '         "Arbeidstakeren kan også utføre arbeid på ulike prosjektsteder i Ringerike og omkringliggende kommuner."),',
        '        ',
        '        ("2. STILLINGSBESKRIVELSE",',
        '         "Arbeidstakeren ansettes som anleggsgartnerfagarbeider og skal utføre arbeidsoppgaver innenfor "',
        '         "anleggsgartnerfaget, inkludert anleggsarbeid, hagearbeid, graving og terrengarbeid."),',
        '        ',
        '        ("3. ANSETTELSESFORHOLD",',
        '         "Oppstart: ________________________________\\n"',
        '         "Ansettelsestype: ☐ Fast ansettelse ☐ Midlertidig ansettelse\\n"',
        '         "Prøvetid: ☐ Ingen prøvetid ☐ _____ måneder (maks 6 måneder)"),',
        '        ',
        '        ("4. ARBEIDSTID",',
        '         "Ordinær arbeidstid: 37,5 timer per uke\\n"',
        '         "Arbeidstidsordning: Dagarbeid, mandag til fredag\\n"',
        '         "Arbeidstid: 07:00 - 15:30 (inkludert 30 min lunsj)"),',
        '        ',
        '        ("5. LØNN OG GODTGJØRELSER",',
        '         "Grunnlønn: Kr ________________________________ per måned\\n"',
        '         "Lønnsutbetaling: Den 5. i hver måned\\n"',
        '         "Kilometergodtgjørelse ved bruk av privat bil: Kr 300,- per måned"),',
        '        ',
        '        ("6. FERIE",',
        '         "Arbeidstakeren har rett til 5 ukers ferie per år i henhold til ferieloven. "',
        '         "Feriepenger utbetales med 12% av opptjent lønn."),',
        '    ]',
        '    ',
        '    # Add all sections',
        '    for title, content in sections:',
        '        doc.add_title(title, style="Heading2") \\',
        '           .add_paragraph(content) \\',
        '           .add_spacer(15)',
        '    ',
        '    # Add signature section',
        '    doc.add_spacer(30) \\',
        '       .add_paragraph("Dato: ________________________________") \\',
        '       .add_spacer(20) \\',
        '       .add_paragraph("Arbeidsgiver: Ringerike Landskap AS") \\',
        '       .add_spacer(30) \\',
        '       .add_paragraph("________________________________") \\',
        '       .add_paragraph("Underskrift arbeidsgiver") \\',
        '       .add_spacer(30) \\',
        '       .add_paragraph("________________________________") \\',
        '       .add_paragraph("Underskrift arbeidstaker")',
        '    ',
        '    # Render the document',
        '    doc.render_to_file("norwegian_contract_ringerike.pdf")',
        '    print("Static Norwegian contract created: norwegian_contract_ringerike.pdf")',
        '',
        '',
        'if __name__ == "__main__":',
        '    create_norwegian_contract()'
    ])
    
    # Write the code to file
    code_content = '\n'.join(code_lines)
    with open("recreate_norwegian_contract.py", 'w', encoding='utf-8') as f:
        f.write(code_content)
    
    print("Custom recreation code generated!")


def generate_field_code_custom(field):
    """Generate custom field code with Norwegian context."""
    field_type = field.get('type', 'text')
    name = field.get('name', 'unnamed')
    rect = field.get('rect', [0, 0, 100, 20])
    value = field.get('value', '')
    
    # Clean up field name for Norwegian context
    clean_name = name.replace(' ', '_').lower()
    
    if len(rect) >= 4:
        x, y, x2, y2 = rect[:4]
        width = abs(x2 - x)
        height = abs(y2 - y)
    else:
        x, y, width, height = 100, 100, 200, 25
    
    # Map common Norwegian field names
    norwegian_field_map = {
        'navn': 'employee_name',
        'name': 'employee_name',
        'adresse': 'address',
        'address': 'address',
        'dato': 'date',
        'date': 'date',
        'lønn': 'salary',
        'salary': 'salary',
        'stilling': 'position',
        'position': 'position'
    }
    
    # Use mapped name if available
    for nor_key, eng_value in norwegian_field_map.items():
        if nor_key in clean_name.lower():
            clean_name = eng_value
            break
    
    if field_type == 'text':
        return f'form.add_text_field("{clean_name}", x={x:.1f}, y={y:.1f}, width={width:.1f}, height={height:.1f}, default_value="{value}")'
    elif field_type in ['button', 'checkbox']:
        return f'form.add_checkbox("{clean_name}", x={x:.1f}, y={y:.1f}, size={min(width, height):.1f}, checked=False)'
    elif field_type == 'signature':
        return f'form.add_signature_field("{clean_name}", x={x:.1f}, y={y:.1f}, width={width:.1f}, height={height:.1f})'
    else:
        return f'# Field: {clean_name} ({field_type}) - position: {x:.1f}, {y:.1f}'


if __name__ == "__main__":
    analyze_norwegian_contract()
