#!/usr/bin/env python3
"""
Final corrected Norwegian employment contract based on systematic research.
Addresses the fundamental ReportLab coordinate system and alignment issues.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import black, grey

def create_final_corrected_contract():
    """Create the final corrected Norwegian employment contract."""
    
    print("Creating final corrected Norwegian contract...")
    
    output_path = "output/final_corrected_contract.pdf"
    os.makedirs("output", exist_ok=True)
    
    # Create canvas
    c = canvas.Canvas(output_path, pagesize=A4)
    width, height = A4
    
    print(f"Page dimensions: {width:.1f} x {height:.1f} points")
    
    # Constants based on research
    LEFT_MARGIN = 44
    FIELD_WIDTH = 505.3
    FIELD_HEIGHT = 15
    LABEL_OFFSET = 14  # Label positioned above field
    SECTION_GAP = 30
    FIELD_GAP = 28
    
    # Document header
    c.setFont("Helvetica-Bold", 16)
    c.setFillColor(black)
    header_y = height - 92
    c.drawString(LEFT_MARGIN, header_y, "Standard arbeidsavtale")
    
    c.setFont("Helvetica", 9)
    c.drawString(LEFT_MARGIN, header_y - 20, "bokmål | september 2024")
    c.setFont("Helvetica", 8)
    c.drawString(LEFT_MARGIN, header_y - 35, "Beholdes av arbeidsgiver – kopi til arbeidstaker")
    
    # Start form content
    current_y = header_y - 70
    
    # Section 1: Arbeidsgiver/virksomhet
    c.setFont("Helvetica-Bold", 9)
    c.drawString(LEFT_MARGIN, current_y, "1. Arbeidsgiver/virksomhet")
    current_y -= SECTION_GAP
    
    # Field 1: Virksomhetens navn
    field_y = current_y
    label_y = field_y + LABEL_OFFSET
    
    c.setFont("Helvetica", 8)
    c.drawString(LEFT_MARGIN, label_y, "Virksomhetens navn:")
    
    c.acroForm.textfield(
        name="virksomhetens_navn",
        x=LEFT_MARGIN,
        y=field_y,
        width=FIELD_WIDTH,
        height=FIELD_HEIGHT,
        value="Ringerike Landskap AS",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    
    # Underline
    c.setStrokeColor(black)
    c.setLineWidth(0.5)
    c.line(LEFT_MARGIN, field_y, LEFT_MARGIN + FIELD_WIDTH, field_y)
    
    current_y -= FIELD_GAP
    
    # Field 2: Organisasjonsnummer
    field_y = current_y
    label_y = field_y + LABEL_OFFSET
    
    c.setFont("Helvetica", 8)
    c.drawString(LEFT_MARGIN, label_y, "Organisasjonsnummer:")
    
    c.acroForm.textfield(
        name="organisasjonsnummer",
        x=LEFT_MARGIN,
        y=field_y,
        width=FIELD_WIDTH,
        height=FIELD_HEIGHT,
        value="123 456 789",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    
    c.line(LEFT_MARGIN, field_y, LEFT_MARGIN + FIELD_WIDTH, field_y)
    current_y -= FIELD_GAP
    
    # Field 3: Adresse
    field_y = current_y
    label_y = field_y + LABEL_OFFSET
    
    c.setFont("Helvetica", 8)
    c.drawString(LEFT_MARGIN, label_y, "Adresse:")
    
    c.acroForm.textfield(
        name="arbeidsgiver_adresse",
        x=LEFT_MARGIN,
        y=field_y,
        width=FIELD_WIDTH,
        height=FIELD_HEIGHT,
        value="Hovedgata 123, 3500 Hønefoss",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    
    c.line(LEFT_MARGIN, field_y, LEFT_MARGIN + FIELD_WIDTH, field_y)
    current_y -= FIELD_GAP + 10
    
    # Section 2: Arbeidstaker
    c.setFont("Helvetica-Bold", 9)
    c.drawString(LEFT_MARGIN, current_y, "2. Arbeidstaker")
    current_y -= SECTION_GAP
    
    # Split fields: Navn and Fødselsdato
    field_y = current_y
    label_y = field_y + LABEL_OFFSET
    
    # Field dimensions for split
    field1_width = 364.8
    field2_x = LEFT_MARGIN + field1_width + 5.4
    field2_width = 137.1
    
    c.setFont("Helvetica", 8)
    c.drawString(LEFT_MARGIN, label_y, "Navn:")
    c.drawString(field2_x, label_y, "Fødselsdato:")
    
    # Name field
    c.acroForm.textfield(
        name="arbeidstaker_navn",
        x=LEFT_MARGIN,
        y=field_y,
        width=field1_width,
        height=FIELD_HEIGHT,
        value="",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    
    # Birth date field
    c.acroForm.textfield(
        name="fodselsdato",
        x=field2_x,
        y=field_y,
        width=field2_width,
        height=FIELD_HEIGHT,
        value="",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    
    # Underlines
    c.line(LEFT_MARGIN, field_y, LEFT_MARGIN + field1_width, field_y)
    c.line(field2_x, field_y, field2_x + field2_width, field_y)
    
    current_y -= FIELD_GAP
    
    # Arbeidstaker adresse
    field_y = current_y
    label_y = field_y + LABEL_OFFSET
    
    c.setFont("Helvetica", 8)
    c.drawString(LEFT_MARGIN, label_y, "Adresse:")
    
    c.acroForm.textfield(
        name="arbeidstaker_adresse",
        x=LEFT_MARGIN,
        y=field_y,
        width=FIELD_WIDTH,
        height=FIELD_HEIGHT,
        value="",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    
    c.line(LEFT_MARGIN, field_y, LEFT_MARGIN + FIELD_WIDTH, field_y)
    current_y -= FIELD_GAP + 10
    
    # Section 3: Arbeidsplass
    c.setFont("Helvetica-Bold", 9)
    c.drawString(LEFT_MARGIN, current_y, "3. Arbeidsplass")
    current_y -= SECTION_GAP
    
    field_y = current_y
    label_y = field_y + LABEL_OFFSET
    
    c.setFont("Helvetica", 8)
    c.drawString(LEFT_MARGIN, label_y, "Adresse:")
    
    c.acroForm.textfield(
        name="arbeidsplass_adresse",
        x=LEFT_MARGIN,
        y=field_y,
        width=FIELD_WIDTH,
        height=FIELD_HEIGHT,
        value="",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    
    c.line(LEFT_MARGIN, field_y, LEFT_MARGIN + FIELD_WIDTH, field_y)
    current_y -= FIELD_GAP + 10
    
    # Section 4: Ansatt som
    c.setFont("Helvetica-Bold", 9)
    c.drawString(LEFT_MARGIN, current_y, "4. Ansatt som")
    current_y -= SECTION_GAP
    
    # Multi-line field
    field_height = 36
    field_y = current_y
    label_y = field_y + field_height + 8
    
    c.setFont("Helvetica", 8)
    c.drawString(LEFT_MARGIN, label_y, "Tittel/beskrivelse av stillingen:")
    
    c.acroForm.textfield(
        name="ansatt_som",
        x=LEFT_MARGIN,
        y=field_y,
        width=FIELD_WIDTH,
        height=field_height,
        value="",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    
    # Multiple underlines for multi-line field
    c.line(LEFT_MARGIN, field_y + 18, LEFT_MARGIN + FIELD_WIDTH, field_y + 18)
    c.line(LEFT_MARGIN, field_y, LEFT_MARGIN + FIELD_WIDTH, field_y)
    
    current_y -= field_height + 20
    
    # Section 5: Arbeidsforholdets varighet og arbeidstid
    c.setFont("Helvetica-Bold", 9)
    c.drawString(LEFT_MARGIN, current_y, "5. Arbeidsforholdets varighet og arbeidstid")
    current_y -= SECTION_GAP
    
    # Note about remaining sections
    c.setFont("Helvetica", 8)
    c.setFillColor(grey)
    c.drawString(LEFT_MARGIN, current_y, "(Seksjoner 6-13 vil bli implementert i neste fase)")
    
    # Footer
    c.setFillColor(black)
    c.setFont("Helvetica", 8)
    c.drawString(LEFT_MARGIN, 57, "AT-563-NB (September 2024) Side 1 av 3")
    
    c.save()
    print(f"Final corrected contract generated: {output_path}")
    return output_path

def main():
    """Main function."""
    
    print("Final Norwegian Contract Correction")
    print("=" * 50)
    print("Research-based systematic corrections:")
    print("• ReportLab bottom-left coordinate system")
    print("• Proper text and form field alignment")
    print("• 14pt label offset above fields")
    print("• Professional spacing and typography")
    print("=" * 50)
    
    try:
        corrected_path = create_final_corrected_contract()
        
        print("\nSUCCESS: Final corrected contract generated!")
        print(f"Output: {corrected_path}")
        
        if os.path.exists(corrected_path):
            size = os.path.getsize(corrected_path)
            print(f"File size: {size} bytes")
        
        print("\nSystematic corrections applied:")
        print("✓ Research-based coordinate handling")
        print("✓ Proper label positioning (14pt above fields)")
        print("✓ Consistent field spacing (28pt between fields)")
        print("✓ Professional typography and alignment")
        print("✓ Split field support (name + birth date)")
        print("✓ Multi-line field with proper underlines")
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
