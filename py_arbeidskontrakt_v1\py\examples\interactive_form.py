"""
Interactive Form Example

Demonstrates creating an interactive PDF form with various field types.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from pdf_engine import PDFEngine


def create_interactive_form():
    """Create an interactive PDF form with various field types."""
    
    # Initialize the PDF engine
    engine = PDFEngine()
    
    # Create a new form
    form = engine.create_form("interactive_form.pdf")
    
    # Add form fields
    form.add_text_field(
        name="full_name",
        x=100, y=700,
        width=200, height=25,
        default_value="",
        required=True
    )
    
    form.add_text_field(
        name="email",
        x=100, y=650,
        width=200, height=25,
        default_value="",
        required=True
    )
    
    form.add_text_field(
        name="phone",
        x=100, y=600,
        width=200, height=25,
        default_value="",
        required=False
    )
    
    # Add checkboxes
    form.add_checkbox(
        name="newsletter",
        x=100, y=550,
        size=15,
        checked=False,
        label="Subscribe to newsletter"
    )
    
    form.add_checkbox(
        name="terms",
        x=100, y=520,
        size=15,
        checked=False,
        label="I agree to the terms and conditions"
    )
    
    # Add radio button group
    radio_options = [
        {"name": "beginner", "x": 100, "y": 470, "label": "Beginner", "selected": False},
        {"name": "intermediate", "x": 100, "y": 440, "label": "Intermediate", "selected": True},
        {"name": "advanced", "x": 100, "y": 410, "label": "Advanced", "selected": False}
    ]
    
    form.add_radio_group("experience_level", radio_options)
    
    # Add signature field
    form.add_signature_field(
        name="signature",
        x=100, y=300,
        width=200, height=50
    )
    
    # Render to file
    form.render_to_file("interactive_form.pdf")
    print("Interactive form created: interactive_form.pdf")
    
    # Also demonstrate rendering to bytes
    pdf_bytes = form.render_to_bytes()
    print(f"Form size: {len(pdf_bytes)} bytes")


if __name__ == "__main__":
    create_interactive_form()
