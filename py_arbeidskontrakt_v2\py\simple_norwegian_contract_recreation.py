#!/usr/bin/env python3
"""
Simple Norwegian Employment Contract Recreation using borb
Based on analysis of the original PDF structure
"""

from decimal import Decimal
from borb.pdf import Document, Page, PDF, SingleColumnLayout, Paragraph
from borb.pdf.canvas.color.color import HexColor


def create_norwegian_employment_contract():
    """Create a Norwegian employment contract based on the analyzed structure"""
    
    # Create document
    document = Document()
    
    # Styling
    black = HexColor("#000000")
    dark_gray = HexColor("#333333")
    
    # Font sizes
    title_size = Decimal(16)
    section_size = Decimal(12)
    text_size = Decimal(10)
    small_size = Decimal(9)
    
    # Page 1
    page1 = Page()
    document.add_page(page1)
    layout1 = SingleColumnLayout(page1)
    
    # Header
    layout1.add(Paragraph(
        "Standard arbeidsavtale",
        font="Helvetica-Bold",
        font_size=title_size,
        font_color=black,
        margin_bottom=Decimal(5)
    ))
    
    layout1.add(Paragraph(
        "bokmål | september 2024",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(10)
    ))
    
    layout1.add(Paragraph(
        "Beholdes av arbeidsgiver – kopi til arbeidstaker",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(5)
    ))
    
    layout1.add(Paragraph(
        "Du finner veiledning for utfylling fra side 4 i dokumentet",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(15)
    ))
    
    # Section 1: Arbeidsgiver/virksomhet
    layout1.add(Paragraph(
        "1. Arbeidsgiver/virksomhet",
        font="Helvetica-Bold",
        font_size=section_size,
        font_color=black,
        margin_top=Decimal(10),
        margin_bottom=Decimal(8)
    ))
    
    layout1.add(Paragraph(
        "Virksomhetens navn: ________________________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(5)
    ))
    
    layout1.add(Paragraph(
        "Virksomhetens organisasjonsnummer: ________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(5)
    ))
    
    layout1.add(Paragraph(
        "Adresse: __________________________________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(10)
    ))
    
    # Section 2: Arbeidstaker
    layout1.add(Paragraph(
        "2. Arbeidstaker",
        font="Helvetica-Bold",
        font_size=section_size,
        font_color=black,
        margin_top=Decimal(10),
        margin_bottom=Decimal(8)
    ))
    
    layout1.add(Paragraph(
        "Navn: ________________________________  Fødselsdato: ____________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(5)
    ))
    
    layout1.add(Paragraph(
        "Adresse: __________________________________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(10)
    ))
    
    # Section 3: Arbeidsplass
    layout1.add(Paragraph(
        "3. Arbeidsplass",
        font="Helvetica-Bold",
        font_size=section_size,
        font_color=black,
        margin_top=Decimal(10),
        margin_bottom=Decimal(8)
    ))
    
    layout1.add(Paragraph(
        "Adresse (adressen til det faste arbeidsstedet): ___________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(10)
    ))
    
    # Section 4: Ansatt som
    layout1.add(Paragraph(
        "4. Ansatt som",
        font="Helvetica-Bold",
        font_size=section_size,
        font_color=black,
        margin_top=Decimal(10),
        margin_bottom=Decimal(8)
    ))
    
    layout1.add(Paragraph(
        "Tittel, stilling, arbeidskategori eller beskrivelse av arbeidet:",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(3)
    ))
    
    layout1.add(Paragraph(
        "________________________________________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(10)
    ))
    
    # Section 5: Arbeidsforholdets varighet og arbeidstid
    layout1.add(Paragraph(
        "5. Arbeidsforholdets varighet og arbeidstid",
        font="Helvetica-Bold",
        font_size=section_size,
        font_color=black,
        margin_top=Decimal(10),
        margin_bottom=Decimal(8)
    ))
    
    layout1.add(Paragraph(
        "Ansatt fra (dato): ________________  Stillingsbrøk (i prosent av 100 % stilling): ______%",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(8)
    ))
    
    layout1.add(Paragraph(
        "[ ] Fast ansatt / fast arbeidsforhold     [ ] Midlertidig ansatt / midlertidig arbeidsforhold",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(8)
    ))
    
    layout1.add(Paragraph(
        "Forventet varighet (sluttdato, omtrentlig antall uker eller forutsetning for avslutning):",
        font="Helvetica",
        font_size=small_size,
        font_color=black,
        margin_bottom=Decimal(3)
    ))
    
    layout1.add(Paragraph(
        "________________________________________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(5)
    ))
    
    layout1.add(Paragraph(
        "Grunnen til at ansettelsen/arbeidsforholdet er midlertidig:",
        font="Helvetica",
        font_size=small_size,
        font_color=black,
        margin_bottom=Decimal(3)
    ))
    
    layout1.add(Paragraph(
        "________________________________________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(5)
    ))
    
    layout1.add(Paragraph(
        "Ukentlig arbeidstid (timer): ____________  Daglig arbeidstid (timer): ____________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(5)
    ))
    
    layout1.add(Paragraph(
        "Arbeidstidens plassering (når skal arbeidet utføres?):",
        font="Helvetica",
        font_size=small_size,
        font_color=black,
        margin_bottom=Decimal(3)
    ))
    
    layout1.add(Paragraph(
        "________________________________________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(10)
    ))
    
    # Footer
    layout1.add(Paragraph(
        "Norwegian - bokmål - Standard contract of employment | AT-563-NB (September 2024) Side 1 av 3",
        font="Helvetica",
        font_size=small_size,
        font_color=dark_gray,
        margin_top=Decimal(20)
    ))
    
    # Page 2
    page2 = Page()
    document.add_page(page2)
    layout2 = SingleColumnLayout(page2)
    
    # Continue Section 5
    layout2.add(Paragraph(
        "5. Arbeidsforholdets varighet og arbeidstid (fortsettelse)",
        font="Helvetica-Bold",
        font_size=section_size,
        font_color=black,
        margin_top=Decimal(20),
        margin_bottom=Decimal(10)
    ))
    
    work_conditions = [
        "Ordninger for å endre vakter i arbeidsplanen:",
        "Ordninger for arbeid utover avtalt arbeidstid (merarbeid/overtid):",
        "Arbeidstakers oppsigelsesfrist og framgangsmåte for å avslutte arbeidsforholdet:",
        "Arbeidsgivers oppsigelsesfrist og framgangsmåte for å avslutte arbeidsforholdet:",
        "Ferietid fastsettes i tråd med ferieloven. Oppgi eventuelt andre regler og avtaler som bestemmer ferietiden:",
        "Eventuelt rett til annet fravær betalt av arbeidsgiver:"
    ]
    
    for condition in work_conditions:
        layout2.add(Paragraph(
            condition,
            font="Helvetica",
            font_size=text_size,
            font_color=black,
            margin_bottom=Decimal(3)
        ))
        layout2.add(Paragraph(
            "________________________________________________________________",
            font="Helvetica",
            font_size=text_size,
            font_color=black,
            margin_bottom=Decimal(8)
        ))
    
    # Section 6: Prøvetid
    layout2.add(Paragraph(
        "6. Eventuell prøvetid",
        font="Helvetica-Bold",
        font_size=section_size,
        font_color=black,
        margin_top=Decimal(15),
        margin_bottom=Decimal(8)
    ))
    
    probation_fields = [
        "Prøvetidens lengde (maksimalt seks måneder eller inntil halvparten av ansettelsens varighet):",
        "Oppsigelsesfrist i prøvetiden:",
        "Eventuell forlengelse av prøvetid:"
    ]
    
    for field in probation_fields:
        layout2.add(Paragraph(
            field,
            font="Helvetica",
            font_size=text_size,
            font_color=black,
            margin_bottom=Decimal(3)
        ))
        layout2.add(Paragraph(
            "________________________________________________________________",
            font="Helvetica",
            font_size=text_size,
            font_color=black,
            margin_bottom=Decimal(5)
        ))
    
    # Section 7: Lønn
    layout2.add(Paragraph(
        "7. Lønn",
        font="Helvetica-Bold",
        font_size=section_size,
        font_color=black,
        margin_top=Decimal(15),
        margin_bottom=Decimal(8)
    ))
    
    salary_fields = [
        "Lønn per time eller måned:",
        "Kontonummer for utbetaling av lønn:",
        "Dato for utbetaling av lønn:",
        "Overtidstillegg (minst 40 prosent av timelønn):",
        "Eventuelt helge-/nattillegg:",
        "Eventuelt andre tillegg (spesifiser og oppgi tilleggene særskilt):",
        "Eventuelle godtgjørelser/diett (spesifiser):",
        "Feriepenger (spesifiser avtale, sats eller grunnlag):"
    ]
    
    for field in salary_fields:
        layout2.add(Paragraph(
            field,
            font="Helvetica",
            font_size=text_size,
            font_color=black,
            margin_bottom=Decimal(3)
        ))
        layout2.add(Paragraph(
            "________________________________________________________________",
            font="Helvetica",
            font_size=text_size,
            font_color=black,
            margin_bottom=Decimal(5)
        ))
    
    # Footer
    layout2.add(Paragraph(
        "Norwegian - bokmål - Standard contract of employment | AT-563-NB (September 2024) Side 2 av 3",
        font="Helvetica",
        font_size=small_size,
        font_color=dark_gray,
        margin_top=Decimal(20)
    ))
    
    # Page 3
    page3 = Page()
    document.add_page(page3)
    layout3 = SingleColumnLayout(page3)
    
    # Section 8: Tariffavtale
    layout3.add(Paragraph(
        "8. Tariffavtale",
        font="Helvetica-Bold",
        font_size=section_size,
        font_color=black,
        margin_top=Decimal(20),
        margin_bottom=Decimal(8)
    ))
    
    layout3.add(Paragraph(
        "Arbeidsforholdet er regulert av følgende tariffavtale:",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(3)
    ))
    
    layout3.add(Paragraph(
        "________________________________________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(5)
    ))
    
    layout3.add(Paragraph(
        "Eventuelle tariffparter (dersom avtalen er inngått av parter utenfor virksomheten):",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(3)
    ))
    
    layout3.add(Paragraph(
        "________________________________________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(10)
    ))
    
    # Remaining sections
    remaining_sections = [
        ("9. Eventuell rett til kompetanseutvikling som arbeidsgiver tilbyr", 
         "Oppgi ev. kompetanseutvikling som arbeidstaker har rett til:"),
        ("10. Ytelser til sosial trygghet i regi av arbeidsgiver", 
         "Oppgi ytelser, og oppgi institusjoner som mottar innbetaling eller finansiering av ytelsene fra arbeidsgiver:"),
        ("11. Innleiers identitet (dersom arbeidstakeren leies ut fra bemanningsforetak)", 
         "Oppgi navnet på innleieren så snart det er kjent:"),
        ("12. Andre opplysninger", "")
    ]
    
    for section_title, field_text in remaining_sections:
        layout3.add(Paragraph(
            section_title,
            font="Helvetica-Bold",
            font_size=section_size,
            font_color=black,
            margin_top=Decimal(15),
            margin_bottom=Decimal(8)
        ))
        
        if field_text:
            layout3.add(Paragraph(
                field_text,
                font="Helvetica",
                font_size=text_size,
                font_color=black,
                margin_bottom=Decimal(3)
            ))
        
        layout3.add(Paragraph(
            "________________________________________________________________",
            font="Helvetica",
            font_size=text_size,
            font_color=black,
            margin_bottom=Decimal(5)
        ))
    
    # Section 13: Signatures
    layout3.add(Paragraph(
        "13. Underskrifter",
        font="Helvetica-Bold",
        font_size=section_size,
        font_color=black,
        margin_top=Decimal(20),
        margin_bottom=Decimal(10)
    ))
    
    layout3.add(Paragraph(
        "Dato: ________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(8)
    ))
    
    layout3.add(Paragraph(
        "For arbeidsgiver: Navn og stilling: ________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(8)
    ))
    
    layout3.add(Paragraph(
        "Underskrift arbeidsgiver: ______________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(8)
    ))
    
    layout3.add(Paragraph(
        "Underskrift arbeidstaker: ______________________________________",
        font="Helvetica",
        font_size=text_size,
        font_color=black,
        margin_bottom=Decimal(15)
    ))
    
    # Footer
    layout3.add(Paragraph(
        "Norwegian - bokmål - Standard contract of employment | AT-563-NB (September 2024) Side 3 av 3",
        font="Helvetica",
        font_size=small_size,
        font_color=dark_gray,
        margin_top=Decimal(30)
    ))
    
    # Save document
    output_path = "recreated_norwegian_employment_contract.pdf"
    with open(output_path, "wb") as pdf_file:
        PDF.dumps(pdf_file, document)
    
    print(f"Norwegian employment contract recreated: {output_path}")
    return output_path


def main():
    """Generate the Norwegian employment contract"""
    print("=== Recreating Norwegian Employment Contract with borb ===")
    print("Based on detailed analysis of the original PDF structure")
    
    output_file = create_norwegian_employment_contract()
    
    print(f"✓ Contract generated: {output_file}")
    print("✓ 3 pages created with form structure")
    print("✓ All 13 sections included")
    print("✓ Norwegian text and formatting preserved")
    print("✓ Form fields with underlines for filling")
    print("✓ Ready for customization and use")
    
    return output_file


if __name__ == "__main__":
    main()
