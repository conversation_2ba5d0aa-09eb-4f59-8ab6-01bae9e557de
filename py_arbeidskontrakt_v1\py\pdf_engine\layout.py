"""
Layout Manager Mo<PERSON><PERSON>

Handles advanced layout operations, positioning, and complex document
structures. Provides utilities for responsive layouts and positioning.
"""

from typing import Dict, Any, Tuple, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

from reportlab.lib.units import inch, cm, mm
from reportlab.lib.pagesizes import A4, letter

from .exceptions import LayoutError, ValidationError


class Alignment(Enum):
    """Text and element alignment options."""
    LEFT = "left"
    CENTER = "center"
    RIGHT = "right"
    JUSTIFY = "justify"


class Units(Enum):
    """Measurement units."""
    POINTS = "points"
    INCHES = "inches"
    CM = "cm"
    MM = "mm"


@dataclass
class Position:
    """Position coordinates with units."""
    x: float
    y: float
    unit: Units = Units.POINTS
    
    def to_points(self) -> Tuple[float, float]:
        """Convert position to points."""
        if self.unit == Units.POINTS:
            return (self.x, self.y)
        elif self.unit == Units.INCHES:
            return (self.x * inch, self.y * inch)
        elif self.unit == Units.CM:
            return (self.x * cm, self.y * cm)
        elif self.unit == Units.MM:
            return (self.x * mm, self.y * mm)
        else:
            raise LayoutError(f"Unsupported unit: {self.unit}")


@dataclass
class Dimensions:
    """Element dimensions with units."""
    width: float
    height: float
    unit: Units = Units.POINTS
    
    def to_points(self) -> Tuple[float, float]:
        """Convert dimensions to points."""
        if self.unit == Units.POINTS:
            return (self.width, self.height)
        elif self.unit == Units.INCHES:
            return (self.width * inch, self.height * inch)
        elif self.unit == Units.CM:
            return (self.width * cm, self.height * cm)
        elif self.unit == Units.MM:
            return (self.width * mm, self.height * mm)
        else:
            raise LayoutError(f"Unsupported unit: {self.unit}")


@dataclass
class Margins:
    """Page margins with units."""
    top: float
    bottom: float
    left: float
    right: float
    unit: Units = Units.POINTS
    
    def to_points(self) -> Dict[str, float]:
        """Convert margins to points."""
        if self.unit == Units.POINTS:
            return {
                'top': self.top,
                'bottom': self.bottom,
                'left': self.left,
                'right': self.right
            }
        elif self.unit == Units.INCHES:
            return {
                'top': self.top * inch,
                'bottom': self.bottom * inch,
                'left': self.left * inch,
                'right': self.right * inch
            }
        elif self.unit == Units.CM:
            return {
                'top': self.top * cm,
                'bottom': self.bottom * cm,
                'left': self.left * cm,
                'right': self.right * cm
            }
        elif self.unit == Units.MM:
            return {
                'top': self.top * mm,
                'bottom': self.bottom * mm,
                'left': self.left * mm,
                'right': self.right * mm
            }
        else:
            raise LayoutError(f"Unsupported unit: {self.unit}")


class LayoutManager:
    """
    Advanced layout management for PDF documents.

    Provides utilities for positioning, alignment, and responsive layouts.
    Enhanced for Norwegian employment contract precise positioning.
    """

    # Norwegian contract specific measurements (from reference document)
    NORWEGIAN_CONTRACT_LAYOUT = {
        'page_size': A4,  # 595.32 x 842.04 points
        'left_margin': 44,  # 44 points as per reference
        'right_margin': 44,
        'top_margin': 57,   # Approximate from reference
        'bottom_margin': 57,
        'field_height': 15.9,  # Standard field height
        'field_width': 505.3,  # Standard field width
        'multi_line_height': 34.9,  # Multi-line field height
        'section_spacing': 30,  # Spacing between sections
        'field_spacing': 28,    # Spacing between fields
        'header_height': 92,    # Header section height
        'footer_height': 57     # Footer section height
    }

    def __init__(self, config: Dict[str, Any]):
        """Initialize layout manager with configuration."""
        self.config = config
        self.default_page_size = config.get('page_size', A4)

        # Use Norwegian contract margins if document type is specified
        if config.get('document_type') == 'norwegian_employment_contract':
            self.default_margins = Margins(
                top=self.NORWEGIAN_CONTRACT_LAYOUT['top_margin'],
                bottom=self.NORWEGIAN_CONTRACT_LAYOUT['bottom_margin'],
                left=self.NORWEGIAN_CONTRACT_LAYOUT['left_margin'],
                right=self.NORWEGIAN_CONTRACT_LAYOUT['right_margin'],
                unit=Units.POINTS
            )
        else:
            self.default_margins = Margins(
                top=2*cm, bottom=2*cm, left=2*cm, right=2*cm, unit=Units.POINTS
            )
    
    def create_grid(self,
                   rows: int,
                   cols: int,
                   page_size: Optional[Tuple[float, float]] = None,
                   margins: Optional[Margins] = None) -> 'GridLayout':
        """
        Create a grid layout system.
        
        Args:
            rows: Number of rows
            cols: Number of columns
            page_size: Optional page size override
            margins: Optional margins override
            
        Returns:
            GridLayout instance
        """
        if rows <= 0 or cols <= 0:
            raise ValidationError("Grid must have positive rows and columns")
            
        page_size = page_size or self.default_page_size
        margins = margins or self.default_margins
        
        return GridLayout(rows, cols, page_size, margins)
    
    def calculate_center_position(self,
                                 element_width: float,
                                 element_height: float,
                                 page_size: Optional[Tuple[float, float]] = None,
                                 margins: Optional[Margins] = None) -> Position:
        """
        Calculate center position for an element.
        
        Args:
            element_width: Element width in points
            element_height: Element height in points
            page_size: Optional page size override
            margins: Optional margins override
            
        Returns:
            Position for centering the element
        """
        page_size = page_size or self.default_page_size
        margins = margins or self.default_margins
        margin_dict = margins.to_points()
        
        available_width = page_size[0] - margin_dict['left'] - margin_dict['right']
        available_height = page_size[1] - margin_dict['top'] - margin_dict['bottom']
        
        center_x = margin_dict['left'] + (available_width - element_width) / 2
        center_y = margin_dict['bottom'] + (available_height - element_height) / 2
        
        return Position(center_x, center_y)
    
    def align_elements_horizontally(self,
                                   elements: List[Dict[str, Any]],
                                   alignment: Alignment,
                                   page_size: Optional[Tuple[float, float]] = None,
                                   margins: Optional[Margins] = None) -> List[Position]:
        """
        Align multiple elements horizontally.
        
        Args:
            elements: List of element dicts with 'width' key
            alignment: Horizontal alignment
            page_size: Optional page size override
            margins: Optional margins override
            
        Returns:
            List of positions for aligned elements
        """
        if not elements:
            return []
            
        page_size = page_size or self.default_page_size
        margins = margins or self.default_margins
        margin_dict = margins.to_points()
        
        available_width = page_size[0] - margin_dict['left'] - margin_dict['right']
        positions = []
        
        if alignment == Alignment.LEFT:
            current_x = margin_dict['left']
            for element in elements:
                positions.append(Position(current_x, 0))  # Y will be set separately
                current_x += element['width']
                
        elif alignment == Alignment.RIGHT:
            total_width = sum(element['width'] for element in elements)
            current_x = page_size[0] - margin_dict['right'] - total_width
            for element in elements:
                positions.append(Position(current_x, 0))
                current_x += element['width']
                
        elif alignment == Alignment.CENTER:
            total_width = sum(element['width'] for element in elements)
            start_x = margin_dict['left'] + (available_width - total_width) / 2
            current_x = start_x
            for element in elements:
                positions.append(Position(current_x, 0))
                current_x += element['width']
        
        return positions

    def get_norwegian_field_position(self, section: int, field_index: int = 0) -> Position:
        """
        Get precise field position for Norwegian employment contract.

        Args:
            section: Section number (1-13)
            field_index: Field index within section (0-based)

        Returns:
            Position for the field
        """
        layout = self.NORWEGIAN_CONTRACT_LAYOUT

        # Base Y positions for each section (from reference document analysis)
        section_y_positions = {
            1: 730,  # Arbeidsgiver/virksomhet
            2: 620,  # Arbeidstaker
            3: 540,  # Arbeidsplass
            4: 480,  # Ansatt som
            5: 420,  # Arbeidsforholdets varighet
            6: 370,  # Prøvetid
            7: 320,  # Lønn
            8: 270,  # Tariffavtale
            9: 220,  # Kompetanseutvikling
            10: 170, # Ytelser
            11: 120, # Innleiers identitet
            12: 70,  # Andre opplysninger
            13: 20   # Underskrifter
        }

        base_y = section_y_positions.get(section, 400)
        field_y = base_y - (field_index * layout['field_spacing'])

        return Position(layout['left_margin'], field_y)

    def get_norwegian_field_dimensions(self, field_type: str = 'standard') -> Dimensions:
        """
        Get field dimensions for Norwegian employment contract.

        Args:
            field_type: Type of field ('standard', 'multi_line', 'split_left', 'split_right')

        Returns:
            Dimensions for the field
        """
        layout = self.NORWEGIAN_CONTRACT_LAYOUT

        if field_type == 'standard':
            return Dimensions(layout['field_width'], layout['field_height'])
        elif field_type == 'multi_line':
            return Dimensions(layout['field_width'], layout['multi_line_height'])
        elif field_type == 'split_left':
            return Dimensions(364.8, layout['field_height'])  # Name field width
        elif field_type == 'split_right':
            return Dimensions(137.1, layout['field_height'])  # Date field width
        else:
            return Dimensions(layout['field_width'], layout['field_height'])

    def calculate_norwegian_page_layout(self, page_number: int) -> Dict[str, Any]:
        """
        Calculate layout information for a specific page of Norwegian contract.

        Args:
            page_number: Page number (1-based)

        Returns:
            Dictionary with layout information for the page
        """
        layout = self.NORWEGIAN_CONTRACT_LAYOUT

        page_layouts = {
            1: {
                'sections': [1, 2, 3, 4, 5],
                'header': True,
                'footer': True,
                'content_area': {
                    'top': layout['top_margin'] + layout['header_height'],
                    'bottom': layout['bottom_margin'] + layout['footer_height']
                }
            },
            2: {
                'sections': [5, 6, 7, 8],
                'header': False,
                'footer': True,
                'content_area': {
                    'top': layout['top_margin'],
                    'bottom': layout['bottom_margin'] + layout['footer_height']
                }
            },
            3: {
                'sections': [8, 9, 10, 11, 12, 13],
                'header': False,
                'footer': True,
                'content_area': {
                    'top': layout['top_margin'],
                    'bottom': layout['bottom_margin'] + layout['footer_height']
                }
            }
        }

        return page_layouts.get(page_number, page_layouts[1])


class GridLayout:
    """
    Grid-based layout system for precise element positioning.
    """
    
    def __init__(self,
                 rows: int,
                 cols: int,
                 page_size: Tuple[float, float],
                 margins: Margins):
        """Initialize grid layout."""
        self.rows = rows
        self.cols = cols
        self.page_size = page_size
        self.margins = margins
        self.margin_dict = margins.to_points()
        
        # Calculate cell dimensions
        available_width = page_size[0] - self.margin_dict['left'] - self.margin_dict['right']
        available_height = page_size[1] - self.margin_dict['top'] - self.margin_dict['bottom']
        
        self.cell_width = available_width / cols
        self.cell_height = available_height / rows
    
    def get_cell_position(self, row: int, col: int) -> Position:
        """
        Get position for a specific grid cell.
        
        Args:
            row: Row index (0-based)
            col: Column index (0-based)
            
        Returns:
            Position of the cell's top-left corner
        """
        if row < 0 or row >= self.rows:
            raise ValidationError(f"Row {row} out of range (0-{self.rows-1})")
        if col < 0 or col >= self.cols:
            raise ValidationError(f"Column {col} out of range (0-{self.cols-1})")
        
        x = self.margin_dict['left'] + col * self.cell_width
        y = self.page_size[1] - self.margin_dict['top'] - (row + 1) * self.cell_height
        
        return Position(x, y)
    
    def get_cell_dimensions(self) -> Dimensions:
        """
        Get dimensions of a single grid cell.
        
        Returns:
            Dimensions of one cell
        """
        return Dimensions(self.cell_width, self.cell_height)
    
    def span_cells(self, start_row: int, start_col: int, 
                   row_span: int, col_span: int) -> Tuple[Position, Dimensions]:
        """
        Get position and dimensions for spanning multiple cells.
        
        Args:
            start_row: Starting row index
            start_col: Starting column index
            row_span: Number of rows to span
            col_span: Number of columns to span
            
        Returns:
            Tuple of (position, dimensions) for the spanned area
        """
        if start_row + row_span > self.rows:
            raise ValidationError("Row span exceeds grid bounds")
        if start_col + col_span > self.cols:
            raise ValidationError("Column span exceeds grid bounds")
        
        position = self.get_cell_position(start_row, start_col)
        dimensions = Dimensions(
            self.cell_width * col_span,
            self.cell_height * row_span
        )
        
        return position, dimensions
