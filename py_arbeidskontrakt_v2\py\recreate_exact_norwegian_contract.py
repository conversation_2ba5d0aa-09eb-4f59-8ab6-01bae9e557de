#!/usr/bin/env python3
"""
Exact Recreation of Norwegian Employment Contract using borb
Based on detailed analysis of the original PDF structure
"""

from decimal import Decimal
from typing import List, Tuple
from borb.pdf import Document, Page, PDF, SingleColumnLayout, Paragraph
from borb.pdf.canvas.color.color import HexColor
from borb.pdf import FlexibleColumnWidthTable, TableCell


class NorwegianContractGenerator:
    """Generate Norwegian employment contract based on original structure"""
    
    def __init__(self):
        self.document = Document()
        
        # Colors matching the original
        self.black = HexColor("#000000")
        self.dark_gray = HexColor("#333333")
        
        # Font sizes based on analysis
        self.title_size = Decimal(14)
        self.header_size = Decimal(12)
        self.section_size = Decimal(11)
        self.text_size = Decimal(10)
        self.small_size = Decimal(9)
    
    def create_header_section(self, layout: SingleColumnLayout):
        """Create the document header"""
        # Main title
        layout.add(Paragraph(
            "Standard arbeidsavtale",
            font="Helvetica-Bold",
            font_size=self.title_size,
            font_color=self.black,
            margin_bottom=Decimal(5)
        ))
        
        # Subtitle
        layout.add(Paragraph(
            "bokmål | september 2024",
            font="Helvetica",
            font_size=self.text_size,
            font_color=self.black,
            margin_bottom=Decimal(10)
        ))
        
        # Instructions
        layout.add(Paragraph(
            "Beholdes av arbeidsgiver – kopi til arbeidstaker",
            font="Helvetica",
            font_size=self.text_size,
            font_color=self.black,
            margin_bottom=Decimal(5)
        ))
        
        layout.add(Paragraph(
            "Du finner veiledning for utfylling fra side 4 i dokumentet",
            font="Helvetica",
            font_size=self.text_size,
            font_color=self.black,
            margin_bottom=Decimal(15)
        ))
    
    def create_form_table(self, layout: SingleColumnLayout, title: str, fields: List[Tuple[str, str]]):
        """Create a form table section"""
        # Section title
        layout.add(Paragraph(
            title,
            font="Helvetica-Bold",
            font_size=self.section_size,
            font_color=self.black,
            margin_top=Decimal(10),
            margin_bottom=Decimal(8)
        ))
        
        # Create table
        table = FlexibleColumnWidthTable(
            number_of_columns=2,
            number_of_rows=len(fields)
        )
        
        for field_label, field_type in fields:
            # Field label
            table.add(TableCell(
                Paragraph(
                    field_label,
                    font="Helvetica",
                    font_size=self.text_size,
                    font_color=self.black
                )
            ))
            
            # Field input area (empty cell with border)
            table.add(TableCell(
                Paragraph(
                    "_" * 40 if field_type == "line" else "",
                    font="Helvetica",
                    font_size=self.text_size,
                    font_color=self.black
                )
            ))
        
        layout.add(table)
    
    def create_page_1(self) -> Page:
        """Create the first page of the contract"""
        page = Page()
        layout = SingleColumnLayout(page)
        
        # Header
        self.create_header_section(layout)
        
        # Section 1: Arbeidsgiver/virksomhet
        self.create_form_table(layout, "1. Arbeidsgiver/virksomhet", [
            ("Virksomhetens navn", "line"),
            ("Virksomhetens organisasjonsnummer", "line"),
            ("Adresse", "line")
        ])
        
        # Section 2: Arbeidstaker
        layout.add(Paragraph(
            "2. Arbeidstaker",
            font="Helvetica-Bold",
            font_size=self.section_size,
            font_color=self.black,
            margin_top=Decimal(15),
            margin_bottom=Decimal(8)
        ))
        
        # Custom table for name and birth date
        name_table = FlexibleColumnWidthTable(number_of_columns=3, number_of_rows=2)
        name_table.add(TableCell(Paragraph("Navn", font="Helvetica", font_size=self.text_size)))
        name_table.add(TableCell(Paragraph("_" * 25, font="Helvetica", font_size=self.text_size)))
        name_table.add(TableCell(Paragraph("Fødselsdato", font="Helvetica", font_size=self.text_size)))
        
        name_table.add(TableCell(Paragraph("Adresse", font="Helvetica", font_size=self.text_size)))
        name_table.add(TableCell(Paragraph("_" * 40, font="Helvetica", font_size=self.text_size)))
        name_table.add(TableCell(Paragraph("", font="Helvetica", font_size=self.text_size)))
        
        layout.add(name_table)
        
        # Section 3: Arbeidsplass
        self.create_form_table(layout, "3. Arbeidsplass", [
            ("Adresse (adressen til det faste arbeidsstedet)", "line")
        ])
        
        # Section 4: Ansatt som
        self.create_form_table(layout, "4. Ansatt som", [
            ("Tittel, stilling, arbeidskategori eller beskrivelse av arbeidet", "line")
        ])
        
        # Section 5: Arbeidsforholdets varighet og arbeidstid
        layout.add(Paragraph(
            "5. Arbeidsforholdets varighet og arbeidstid",
            font="Helvetica-Bold",
            font_size=self.section_size,
            font_color=self.black,
            margin_top=Decimal(15),
            margin_bottom=Decimal(8)
        ))
        
        # Employment details table
        emp_table = FlexibleColumnWidthTable(number_of_columns=4, number_of_rows=8)
        
        # Row 1: Date and percentage
        emp_table.add(TableCell(Paragraph("Ansatt fra (dato)", font="Helvetica", font_size=self.text_size)))
        emp_table.add(TableCell(Paragraph("_" * 15, font="Helvetica", font_size=self.text_size)))
        emp_table.add(TableCell(Paragraph("", font="Helvetica", font_size=self.text_size)))
        emp_table.add(TableCell(Paragraph("Stillingsbrøk (i prosent av 100 % stilling)", font="Helvetica", font_size=self.text_size)))
        
        # Row 2: Employment type
        emp_table.add(TableCell(Paragraph("☐ Fast ansatt / fast arbeidsforhold", font="Helvetica", font_size=self.text_size)))
        emp_table.add(TableCell(Paragraph("", font="Helvetica", font_size=self.text_size)))
        emp_table.add(TableCell(Paragraph("☐ Midlertidig ansatt / midlertidig arbeidsforhold", font="Helvetica", font_size=self.text_size)))
        emp_table.add(TableCell(Paragraph("", font="Helvetica", font_size=self.text_size)))
        
        # Additional rows for work time details
        work_fields = [
            ("Forventet varighet (sluttdato, omtrentlig antall uker eller forutsetning for avslutning)", ""),
            ("Grunnen til at ansettelsen/arbeidsforholdet er midlertidig", ""),
            ("Ukentlig arbeidstid (timer)", ""),
            ("Daglig arbeidstid (timer)", ""),
            ("Arbeidstidens plassering (når skal arbeidet utføres?)", ""),
            ("Periode/tidspunkt for arbeidet dersom arbeidet skal utføres i ulike perioder eller på ulike tidspunkt/dager, eller hvis daglig/ukentlig arbeidstid varierer", "")
        ]
        
        for field, _ in work_fields:
            emp_table.add(TableCell(Paragraph(field, font="Helvetica", font_size=self.small_size)))
            emp_table.add(TableCell(Paragraph("_" * 20, font="Helvetica", font_size=self.text_size)))
            emp_table.add(TableCell(Paragraph("", font="Helvetica", font_size=self.text_size)))
            emp_table.add(TableCell(Paragraph("", font="Helvetica", font_size=self.text_size)))
        
        layout.add(emp_table)
        
        # Footer
        layout.add(Paragraph(
            "Norwegian - bokmål - Standard contract of employment | AT-563-NB (September 2024) Side 1 av 3",
            font="Helvetica",
            font_size=self.small_size,
            font_color=self.dark_gray,
            margin_top=Decimal(20)
        ))
        
        return page
    
    def create_page_2(self) -> Page:
        """Create the second page of the contract"""
        page = Page()
        layout = SingleColumnLayout(page)
        
        # Continue Section 5
        layout.add(Paragraph(
            "5. Arbeidsforholdets varighet og arbeidstid (fortsettelse)",
            font="Helvetica-Bold",
            font_size=self.section_size,
            font_color=self.black,
            margin_top=Decimal(20),
            margin_bottom=Decimal(10)
        ))
        
        # Additional work conditions
        work_conditions = [
            "Ordninger for å endre vakter i arbeidsplanen",
            "Ordninger for arbeid utover avtalt arbeidstid (merarbeid/overtid)",
            "Arbeidstakers oppsigelsesfrist og framgangsmåte for å avslutte arbeidsforholdet",
            "Arbeidsgivers oppsigelsesfrist og framgangsmåte for å avslutte arbeidsforholdet",
            "Ferietid fastsettes i tråd med ferieloven. Oppgi eventuelt andre regler og avtaler som bestemmer ferietiden",
            "Eventuelt rett til annet fravær betalt av arbeidsgiver"
        ]
        
        for condition in work_conditions:
            layout.add(Paragraph(
                condition,
                font="Helvetica",
                font_size=self.text_size,
                font_color=self.black,
                margin_bottom=Decimal(5)
            ))
            layout.add(Paragraph(
                "_" * 60,
                font="Helvetica",
                font_size=self.text_size,
                font_color=self.black,
                margin_bottom=Decimal(8)
            ))
        
        # Section 6: Prøvetid
        self.create_form_table(layout, "6. Eventuell prøvetid", [
            ("Prøvetidens lengde (maksimalt seks måneder eller inntil halvparten av ansettelsens varighet)", "line"),
            ("Oppsigelsesfrist i prøvetiden", "line"),
            ("Eventuell forlengelse av prøvetid", "line")
        ])
        
        # Section 7: Lønn
        salary_fields = [
            ("Lønn per time eller måned", "line"),
            ("Kontonummer for utbetaling av lønn", "line"),
            ("Dato for utbetaling av lønn", "line"),
            ("Overtidstillegg (minst 40 prosent av timelønn)", "line"),
            ("Eventuelt helge-/nattillegg", "line"),
            ("Eventuelt andre tillegg (spesifiser og oppgi tilleggene særskilt)", "line"),
            ("Eventuelle godtgjørelser/diett (spesifiser)", "line"),
            ("Feriepenger (spesifiser avtale, sats eller grunnlag)", "line")
        ]
        
        self.create_form_table(layout, "7. Lønn", salary_fields)
        
        # Section 8: Tariffavtale
        self.create_form_table(layout, "8. Tariffavtale", [
            ("Arbeidsforholdet er regulert av følgende tariffavtale:", "line")
        ])
        
        # Footer
        layout.add(Paragraph(
            "Norwegian - bokmål - Standard contract of employment | AT-563-NB (September 2024) Side 2 av 3",
            font="Helvetica",
            font_size=self.small_size,
            font_color=self.dark_gray,
            margin_top=Decimal(20)
        ))
        
        return page
    
    def create_page_3(self) -> Page:
        """Create the third page of the contract"""
        page = Page()
        layout = SingleColumnLayout(page)
        
        # Continue Section 8
        self.create_form_table(layout, "8. Tariffavtale (fortsettelse)", [
            ("Eventuelle tariffparter (dersom avtalen er inngått av parter utenfor virksomheten)", "line")
        ])
        
        # Remaining sections
        remaining_sections = [
            ("9. Eventuell rett til kompetanseutvikling som arbeidsgiver tilbyr", [
                ("Oppgi ev. kompetanseutvikling som arbeidstaker har rett til", "line")
            ]),
            ("10. Ytelser til sosial trygghet i regi av arbeidsgiver", [
                ("Oppgi ytelser, og oppgi institusjoner som mottar innbetaling eller finansiering av ytelsene fra arbeidsgiver", "line")
            ]),
            ("11. Innleiers identitet (dersom arbeidstakeren leies ut fra bemanningsforetak)", [
                ("Oppgi navnet på innleieren så snart det er kjent", "line")
            ]),
            ("12. Andre opplysninger", [
                ("", "area")
            ])
        ]
        
        for section_title, fields in remaining_sections:
            self.create_form_table(layout, section_title, fields)
        
        # Section 13: Signatures
        layout.add(Paragraph(
            "13. Underskrifter",
            font="Helvetica-Bold",
            font_size=self.section_size,
            font_color=self.black,
            margin_top=Decimal(15),
            margin_bottom=Decimal(10)
        ))
        
        # Signature table
        sig_table = FlexibleColumnWidthTable(number_of_columns=2, number_of_rows=4)
        
        sig_table.add(TableCell(Paragraph("Dato", font="Helvetica", font_size=self.text_size)))
        sig_table.add(TableCell(Paragraph("_" * 20, font="Helvetica", font_size=self.text_size)))
        
        sig_table.add(TableCell(Paragraph("For arbeidsgiver: Navn og stilling", font="Helvetica", font_size=self.text_size)))
        sig_table.add(TableCell(Paragraph("_" * 30, font="Helvetica", font_size=self.text_size)))
        
        sig_table.add(TableCell(Paragraph("Underskrift arbeidsgiver", font="Helvetica", font_size=self.text_size)))
        sig_table.add(TableCell(Paragraph("_" * 30, font="Helvetica", font_size=self.text_size)))
        
        sig_table.add(TableCell(Paragraph("Underskrift arbeidstaker", font="Helvetica", font_size=self.text_size)))
        sig_table.add(TableCell(Paragraph("_" * 30, font="Helvetica", font_size=self.text_size)))
        
        layout.add(sig_table)
        
        # Footer
        layout.add(Paragraph(
            "Norwegian - bokmål - Standard contract of employment | AT-563-NB (September 2024) Side 3 av 3",
            font="Helvetica",
            font_size=self.small_size,
            font_color=self.dark_gray,
            margin_top=Decimal(30)
        ))
        
        return page
    
    def generate_contract(self, output_path: str = "recreated_exact_norwegian_contract.pdf"):
        """Generate the complete contract"""
        # Add all pages
        self.document.add_page(self.create_page_1())
        self.document.add_page(self.create_page_2())
        self.document.add_page(self.create_page_3())
        
        # Save the document
        with open(output_path, "wb") as pdf_file:
            PDF.dumps(pdf_file, self.document)
        
        return output_path


def main():
    """Generate the Norwegian employment contract"""
    print("=== Generating Exact Norwegian Employment Contract ===")
    print("Based on detailed analysis of the original PDF structure")
    
    generator = NorwegianContractGenerator()
    output_file = generator.generate_contract()
    
    print(f"✓ Contract generated: {output_file}")
    print("✓ 3 pages created with exact form structure")
    print("✓ All sections and fields included")
    print("✓ Ready for customization and use")
    
    print("\n=== Features ===")
    print("- Exact section structure from original")
    print("- Proper Norwegian text and formatting")
    print("- Form fields with underlines for filling")
    print("- Checkbox options for employment type")
    print("- Signature section")
    print("- Page footers with document reference")
    
    return output_file


if __name__ == "__main__":
    main()
