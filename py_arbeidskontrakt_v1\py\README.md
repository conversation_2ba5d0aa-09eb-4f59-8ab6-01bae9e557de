# PDF Engine - Modular PDF Generation System

A unified, enterprise-grade PDF generation framework with clean abstraction layers, minimal dependencies, and extensible architecture for static and interactive documents.

## Features

- **Modular Architecture**: Clean separation of concerns with dedicated modules for documents, forms, and layouts
- **Minimal Dependencies**: Built on ReportLab with minimal external dependencies
- **Interactive Forms**: Support for text fields, checkboxes, radio buttons, and signature fields
- **Advanced Layouts**: Grid-based positioning and responsive layout management
- **PDF Analysis & Reverse Engineering**: Extract structure and generate code from existing PDFs
- **Norwegian Contract Support**: Specialized tools for Norwegian employment contracts
- **Extensible Design**: Plugin-style architecture for future enhancements
- **Enterprise Ready**: Designed for maintainability, scalability, and long-term stability

## Installation

1. **Activate your virtual environment:**
   ```bash
   cd py
   venv\Scripts\activate  # Windows
   # or
   source venv/bin/activate  # Linux/Mac
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## Quick Start

### Basic Document Creation

```python
from pdf_engine import PDFEngine

# Initialize the engine
engine = PDFEngine()

# Create a document
doc = engine.create_document("my_document.pdf")

# Add content using method chaining
doc.add_title("My Document") \
   .add_paragraph("This is a sample paragraph.") \
   .add_table([
       ["Name", "Age", "City"],
       ["John", "30", "Oslo"],
       ["Jane", "25", "Bergen"]
   ]) \
   .render_to_file("my_document.pdf")
```

### Interactive Form Creation

```python
from pdf_engine import PDFEngine

# Initialize the engine
engine = PDFEngine()

# Create a form
form = engine.create_form("my_form.pdf")

# Add form fields
form.add_text_field("name", x=100, y=700, width=200, height=25) \
    .add_checkbox("newsletter", x=100, y=650, label="Subscribe") \
    .add_signature_field("signature", x=100, y=500, width=200, height=50) \
    .render_to_file("my_form.pdf")
```

## Architecture

The PDF Engine follows a modular architecture with clear separation of concerns:

```
pdf_engine/
├── __init__.py          # Main exports
├── core.py              # Central engine coordination
├── document.py          # Static document generation
├── forms.py             # Interactive form creation
├── layout.py            # Advanced layout management
└── exceptions.py        # Custom exception hierarchy
```

### Core Modules

- **PDFEngine**: Central coordinator that provides unified interface
- **DocumentBuilder**: Handles static PDF creation with text, tables, images
- **FormBuilder**: Manages interactive forms with various field types
- **LayoutManager**: Provides advanced positioning and grid layouts

## Examples

The `examples/` directory contains practical demonstrations:

- `basic_document.py`: Simple document with text and tables
- `interactive_form.py`: Form with various field types
- `arbeidskontrakt_generator.py`: Norwegian employment contract generator

## PDF Analysis & Reverse Engineering

The system includes powerful tools to analyze existing PDFs and generate Python code to recreate them:

### Supported Analysis Features

- **Form Field Extraction**: Automatically detect and extract interactive form fields
- **Text Content Analysis**: Extract and analyze text content and structure
- **Layout Detection**: Identify tables, text blocks, and positioning
- **Metadata Extraction**: Get document properties and creation info
- **Code Generation**: Automatically generate Python code to recreate the PDF

### Analysis Libraries Used

- **PyPDF2**: Form field extraction and basic PDF reading
- **pdfplumber**: Advanced text and layout extraction
- **PyMuPDF (fitz)**: Comprehensive PDF manipulation and analysis
- **pdfminer.six**: Deep PDF structure analysis

### Using the PDF Analyzer

```python
from pdf_analyzer import PDFAnalyzer

# Analyze any PDF file
analyzer = PDFAnalyzer("path/to/your/document.pdf")
results = analyzer.analyze_complete()

# Generate Python code to recreate it
code = analyzer.generate_code("recreated_document.py")

# Save detailed analysis
analyzer.save_analysis("analysis_results.json")
```

### Command Line Usage

```bash
# Analyze any PDF
python pdf_analyzer.py "path/to/document.pdf"

# Analyze Norwegian employment contract specifically
python analyze_norwegian_contract.py
```

### Running Examples

```bash
cd examples
python basic_document.py
python interactive_form.py
python arbeidskontrakt_generator.py
```

## Advanced Usage

### Grid-Based Layouts

```python
from pdf_engine import PDFEngine
from pdf_engine.layout import Margins, Units

engine = PDFEngine()
layout = engine.get_layout_manager()

# Create a 4x3 grid
grid = layout.create_grid(
    rows=4, cols=3,
    margins=Margins(top=2, bottom=2, left=2, right=2, unit=Units.CM)
)

# Get position for specific cell
position = grid.get_cell_position(row=1, col=2)
```

### Custom Styling

```python
# Add custom table styling
table_style = [
    ('BACKGROUND', (0, 0), (-1, 0), '#4472C4'),
    ('TEXTCOLOR', (0, 0), (-1, 0), 'white'),
    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
    ('FONTSIZE', (0, 0), (-1, 0), 14),
    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
    ('BACKGROUND', (0, 1), (-1, -1), '#F2F2F2'),
    ('GRID', (0, 0), (-1, -1), 1, 'black')
]

doc.add_table(data, style=table_style)
```

## Extension Points

The system is designed for extensibility:

1. **Custom Field Types**: Extend FormBuilder with new field types
2. **Layout Algorithms**: Add new positioning strategies to LayoutManager
3. **Document Templates**: Create reusable document templates
4. **Validation Rules**: Add custom validation for form fields

## Best Practices

1. **Use Method Chaining**: Build documents fluently with chained method calls
2. **Leverage Grid Layouts**: Use grid system for consistent positioning
3. **Validate Early**: Check inputs at the earliest possible stage
4. **Handle Exceptions**: Wrap operations in try-catch blocks
5. **Reuse Instances**: Create engine once, reuse for multiple documents

## Error Handling

The system provides a comprehensive exception hierarchy:

```python
from pdf_engine.exceptions import PDFEngineError, ValidationError

try:
    doc.add_title("")  # Empty title
except ValidationError as e:
    print(f"Validation error: {e}")
except PDFEngineError as e:
    print(f"PDF engine error: {e}")
```

## Configuration

Configure the engine with custom settings:

```python
config = {
    'page_size': A4,
    'default_font': 'Helvetica',
    'default_font_size': 12
}

engine = PDFEngine(config)
```

## Contributing

1. Follow the modular architecture principles
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Maintain backward compatibility
5. Use type hints for all public APIs

## License

This project is designed for enterprise use with focus on maintainability and extensibility.
