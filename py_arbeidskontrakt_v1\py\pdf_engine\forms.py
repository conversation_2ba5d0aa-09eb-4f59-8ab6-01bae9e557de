"""
Forms Builder Module

Handles interactive PDF form creation with support for text fields,
checkboxes, radio buttons, and other AcroForm elements.
Enhanced for Norwegian employment contract forms with precise positioning.
"""

from typing import Optional, Dict, Any, Union, List, Tuple
from pathlib import Path
import io

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import inch, cm, mm
from reportlab.pdfbase import pdfform
from reportlab.lib.colors import black, white, blue, grey
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import Paragraph

from .exceptions import ValidationError, FormError, RenderError


class FormBuilder:
    """
    Builder for interactive PDF forms.

    Provides high-level interface for creating forms with various
    interactive elements and validation.
    Enhanced for Norwegian employment contract forms.
    """

    # Norwegian contract form field definitions (from reference document)
    NORWEGIAN_CONTRACT_FIELDS = {
        'virksomhetens_navn': {'x': 43.8, 'y': 701.2, 'width': 505.3, 'height': 15.9},
        'organisasjonsnummer': {'x': 43.8, 'y': 672.3, 'width': 505.3, 'height': 15.9},
        'arbeidsgiver_adresse': {'x': 43.8, 'y': 644.2, 'width': 505.3, 'height': 15.9},
        'arbeidstaker_navn': {'x': 43.8, 'y': 591.4, 'width': 364.8, 'height': 15.9},
        'fodselsdato': {'x': 414.0, 'y': 591.4, 'width': 137.1, 'height': 15.9},
        'arbeidstaker_adresse': {'x': 43.8, 'y': 563.2, 'width': 505.3, 'height': 15.9},
        'arbeidsplass_adresse': {'x': 43.8, 'y': 510.7, 'width': 505.3, 'height': 15.9},
        'ansatt_som': {'x': 43.8, 'y': 438.8, 'width': 505.3, 'height': 36.2},
        # Additional fields would be defined here based on the reference document
    }

    def __init__(self, config: Dict[str, Any]):
        """Initialize form builder with configuration."""
        self.config = config
        self._forms = {}

    def new_form(self,
                output_path: Optional[Union[str, Path]] = None,
                page_size: tuple = A4,
                **kwargs) -> 'FormInstance':
        """
        Create a new form instance.

        Args:
            output_path: Optional output file path
            page_size: Page size tuple
            **kwargs: Additional form configuration options

        Returns:
            FormInstance for method chaining
        """
        # Merge additional configuration
        form_config = {**self.config, **kwargs}

        form_instance = FormInstance(
            output_path=output_path,
            page_size=page_size,
            config=form_config
        )

        form_id = id(form_instance)
        self._forms[form_id] = form_instance
        return form_instance

    def create_norwegian_contract_form(self,
                                      output_path: Optional[Union[str, Path]] = None) -> 'NorwegianContractForm':
        """
        Create a Norwegian employment contract form with predefined fields.

        Args:
            output_path: Optional output file path

        Returns:
            NorwegianContractForm instance
        """
        return NorwegianContractForm(
            output_path=output_path,
            config=self.config,
            field_definitions=self.NORWEGIAN_CONTRACT_FIELDS
        )


class FormInstance:
    """
    Individual form instance with fluent interface.
    
    Supports method chaining for building interactive forms.
    """
    
    def __init__(self,
                 output_path: Optional[Union[str, Path]],
                 page_size: tuple,
                 config: Dict[str, Any]):
        """Initialize form instance."""
        self.output_path = output_path
        self.page_size = page_size
        self.config = config
        self.fields = []
        self.canvas = None
        
    def add_text_field(self,
                      name: str,
                      x: float,
                      y: float,
                      width: float = 200,
                      height: float = 20,
                      default_value: str = "",
                      required: bool = False,
                      font_name: str = "Helvetica",
                      font_size: int = 9,
                      border_style: str = "underline",
                      label: str = "",
                      label_offset: float = 14) -> 'FormInstance':
        """
        Add a text input field to the form.

        Args:
            name: Field name (must be unique)
            x: X coordinate
            y: Y coordinate
            width: Field width
            height: Field height
            default_value: Default text value
            required: Whether field is required
            font_name: Font name for the field
            font_size: Font size for the field
            border_style: Border style ('underline', 'box', 'none')
            label: Optional label text to display above field
            label_offset: Vertical offset for label above field (default 14pt)

        Returns:
            Self for method chaining
        """
        if not name:
            raise ValidationError("Field name cannot be empty")

        field = {
            'type': 'text',
            'name': name,
            'x': x,
            'y': y,
            'width': width,
            'height': height,
            'default_value': default_value,
            'required': required,
            'font_name': font_name,
            'font_size': font_size,
            'border_style': border_style,
            'label': label,
            'label_offset': label_offset
        }

        self.fields.append(field)
        return self
    
    def add_checkbox(self,
                    name: str,
                    x: float,
                    y: float,
                    size: float = 12,
                    checked: bool = False,
                    label: str = "") -> 'FormInstance':
        """
        Add a checkbox to the form.
        
        Args:
            name: Field name (must be unique)
            x: X coordinate
            y: Y coordinate
            size: Checkbox size
            checked: Default checked state
            label: Optional label text
            
        Returns:
            Self for method chaining
        """
        if not name:
            raise ValidationError("Field name cannot be empty")
            
        field = {
            'type': 'checkbox',
            'name': name,
            'x': x,
            'y': y,
            'size': size,
            'checked': checked,
            'label': label
        }
        
        self.fields.append(field)
        return self
    
    def add_radio_group(self,
                       group_name: str,
                       options: List[Dict[str, Any]]) -> 'FormInstance':
        """
        Add a radio button group to the form.
        
        Args:
            group_name: Group name (must be unique)
            options: List of option dicts with keys: name, x, y, label, selected
            
        Returns:
            Self for method chaining
        """
        if not group_name:
            raise ValidationError("Group name cannot be empty")
        if not options:
            raise ValidationError("Radio group must have at least one option")
            
        for option in options:
            if 'name' not in option or 'x' not in option or 'y' not in option:
                raise ValidationError("Radio option must have name, x, and y")
                
            field = {
                'type': 'radio',
                'group_name': group_name,
                'name': option['name'],
                'x': option['x'],
                'y': option['y'],
                'label': option.get('label', ''),
                'selected': option.get('selected', False)
            }
            
            self.fields.append(field)
        
        return self
    
    def add_signature_field(self,
                           name: str,
                           x: float,
                           y: float,
                           width: float = 200,
                           height: float = 50) -> 'FormInstance':
        """
        Add a signature field to the form.
        
        Args:
            name: Field name (must be unique)
            x: X coordinate
            y: Y coordinate
            width: Field width
            height: Field height
            
        Returns:
            Self for method chaining
        """
        if not name:
            raise ValidationError("Field name cannot be empty")
            
        field = {
            'type': 'signature',
            'name': name,
            'x': x,
            'y': y,
            'width': width,
            'height': height
        }
        
        self.fields.append(field)
        return self
    
    def render_to_bytes(self) -> bytes:
        """
        Render form to bytes.
        
        Returns:
            PDF content as bytes
        """
        try:
            buffer = io.BytesIO()
            c = canvas.Canvas(buffer, pagesize=self.page_size)
            
            # Render all form fields
            for field in self.fields:
                self._render_field(c, field)
            
            c.save()
            return buffer.getvalue()
        except Exception as e:
            raise RenderError(f"Failed to render form: {e}") from e
    
    def render_to_file(self, output_path: Union[str, Path]) -> None:
        """
        Render form to file.
        
        Args:
            output_path: Output file path
        """
        try:
            c = canvas.Canvas(str(output_path), pagesize=self.page_size)
            
            # Render all form fields
            for field in self.fields:
                self._render_field(c, field)
            
            c.save()
        except Exception as e:
            raise RenderError(f"Failed to save form to {output_path}: {e}") from e
    
    def _render_field(self, canvas_obj: canvas.Canvas, field: Dict[str, Any]) -> None:
        """
        Render individual form field with professional styling.

        Args:
            canvas_obj: ReportLab canvas object
            field: Field configuration dictionary
        """
        field_type = field['type']

        if field_type == 'text':
            # Set font for the field if specified
            font_name = field.get('font_name', 'Helvetica')
            font_size = field.get('font_size', 9)
            border_style = field.get('border_style', 'underline')
            label = field.get('label', '')
            label_offset = field.get('label_offset', 14)

            # Add label if provided - positioned above the field
            if label:
                canvas_obj.setFont("Helvetica", 8)
                canvas_obj.setFillColor(black)
                canvas_obj.drawString(
                    field['x'],
                    field['y'] + label_offset,
                    label
                )

            # Create text field with professional styling
            canvas_obj.acroForm.textfield(
                name=field['name'],
                x=field['x'],
                y=field['y'],
                width=field['width'],
                height=field['height'],
                value=field['default_value'],
                forceBorder=(border_style == 'box'),
                fontName=font_name,
                fontSize=font_size
            )

            # Add underline styling for Norwegian contract fields
            if border_style == 'underline':
                canvas_obj.setStrokeColor(black)
                canvas_obj.setLineWidth(0.5)
                canvas_obj.line(
                    field['x'],
                    field['y'],
                    field['x'] + field['width'],
                    field['y']
                )
        
        elif field_type == 'checkbox':
            canvas_obj.acroForm.checkbox(
                name=field['name'],
                x=field['x'],
                y=field['y'],
                size=field['size'],
                checked=field['checked'],
                forceBorder=True
            )
            
            # Add label if provided
            if field['label']:
                canvas_obj.drawString(
                    field['x'] + field['size'] + 5,
                    field['y'],
                    field['label']
                )
        
        elif field_type == 'radio':
            canvas_obj.acroForm.radio(
                name=field['group_name'],
                value=field['name'],
                x=field['x'],
                y=field['y'],
                selected=field['selected']
            )
            
            # Add label if provided
            if field['label']:
                canvas_obj.drawString(
                    field['x'] + 15,
                    field['y'],
                    field['label']
                )
        
        elif field_type == 'signature':
            # Create signature field as text field with special formatting
            canvas_obj.acroForm.textfield(
                name=field['name'],
                x=field['x'],
                y=field['y'],
                width=field['width'],
                height=field['height'],
                value="",
                forceBorder=True
            )
            
            # Add signature line
            canvas_obj.line(
                field['x'],
                field['y'],
                field['x'] + field['width'],
                field['y']
            )


class NorwegianContractForm(FormInstance):
    """
    Specialized form for Norwegian employment contracts.

    Provides predefined fields and professional styling matching
    the official Norwegian employment contract template.
    """

    def __init__(self,
                 output_path: Optional[Union[str, Path]],
                 config: Dict[str, Any],
                 field_definitions: Dict[str, Dict[str, float]]):
        """Initialize Norwegian contract form."""
        super().__init__(output_path, A4, config)
        self.field_definitions = field_definitions
        self._setup_norwegian_fields()

    def _setup_norwegian_fields(self) -> None:
        """Setup all Norwegian contract fields with proper positioning."""
        # Section 1: Arbeidsgiver/virksomhet
        self.add_text_field(
            name="virksomhetens_navn",
            x=self.field_definitions['virksomhetens_navn']['x'],
            y=self.field_definitions['virksomhetens_navn']['y'],
            width=self.field_definitions['virksomhetens_navn']['width'],
            height=self.field_definitions['virksomhetens_navn']['height'],
            font_name="Helvetica",  # Would be Calibri in production
            font_size=9,
            border_style="underline"
        )

        self.add_text_field(
            name="organisasjonsnummer",
            x=self.field_definitions['organisasjonsnummer']['x'],
            y=self.field_definitions['organisasjonsnummer']['y'],
            width=self.field_definitions['organisasjonsnummer']['width'],
            height=self.field_definitions['organisasjonsnummer']['height'],
            font_name="Helvetica",
            font_size=9,
            border_style="underline"
        )

        self.add_text_field(
            name="arbeidsgiver_adresse",
            x=self.field_definitions['arbeidsgiver_adresse']['x'],
            y=self.field_definitions['arbeidsgiver_adresse']['y'],
            width=self.field_definitions['arbeidsgiver_adresse']['width'],
            height=self.field_definitions['arbeidsgiver_adresse']['height'],
            font_name="Helvetica",
            font_size=9,
            border_style="underline"
        )

        # Section 2: Arbeidstaker
        self.add_text_field(
            name="arbeidstaker_navn",
            x=self.field_definitions['arbeidstaker_navn']['x'],
            y=self.field_definitions['arbeidstaker_navn']['y'],
            width=self.field_definitions['arbeidstaker_navn']['width'],
            height=self.field_definitions['arbeidstaker_navn']['height'],
            font_name="Helvetica",
            font_size=9,
            border_style="underline"
        )

        self.add_text_field(
            name="fodselsdato",
            x=self.field_definitions['fodselsdato']['x'],
            y=self.field_definitions['fodselsdato']['y'],
            width=self.field_definitions['fodselsdato']['width'],
            height=self.field_definitions['fodselsdato']['height'],
            font_name="Helvetica",
            font_size=9,
            border_style="underline"
        )

        # Additional fields would be added here following the same pattern

    def add_section_header(self, section_number: int, title: str, y_position: float) -> None:
        """
        Add a section header with Norwegian contract styling.

        Args:
            section_number: Section number (1-13)
            title: Section title in Norwegian
            y_position: Y position for the header
        """
        # This would be implemented to add section headers
        # For now, we'll add it as a text element
        pass

    def validate_norwegian_compliance(self) -> Dict[str, Any]:
        """
        Validate that the form meets Norwegian employment law requirements.

        Returns:
            Validation results dictionary
        """
        validation_results = {
            'compliant': True,
            'missing_fields': [],
            'warnings': []
        }

        # Check for required fields
        required_fields = [
            'virksomhetens_navn', 'organisasjonsnummer', 'arbeidsgiver_adresse',
            'arbeidstaker_navn', 'fodselsdato', 'arbeidstaker_adresse'
        ]

        existing_field_names = [field['name'] for field in self.fields]

        for required_field in required_fields:
            if required_field not in existing_field_names:
                validation_results['missing_fields'].append(required_field)
                validation_results['compliant'] = False

        return validation_results
