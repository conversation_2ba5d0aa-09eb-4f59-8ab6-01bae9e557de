#!/usr/bin/env python3
"""
Norwegian Employment Contract Recreation using borb
Generated from analysis of: Norwegian - bokmal - Standard contract of employment-unlocked.pdf
Source parsing method: pdfplumber
Total pages: 5
"""

from decimal import Decimal
from borb.pdf import Document, Page, PDF, SingleColumnLayout, Paragraph
from borb.pdf.canvas.color.color import HexColor
from borb.pdf.canvas.layout.table import Table, TableCell, FlexibleColumnWidthTable

def create_norwegian_employment_contract():
    """Create a Norwegian employment contract based on the analyzed structure"""
    
    # Create document
    document = Document()
    
    # Styling
    black = HexColor("#000000")
    dark_blue = HexColor("#1f4e79")
    
    # Create 5 page(s) based on original
    for page_num in range(5):
        page = Page()
        document.add_page(page)
        layout = SingleColumnLayout(page)
        
        if page_num == 0:
            # Main contract page
            layout.add(Paragraph(
                "ARBEIDSAVTALE",
                font="Helvetica-Bold",
                font_size=Decimal(18),
                font_color=black,
                margin_top=Decimal(30),
                margin_bottom=Decimal(25)
            ))
            
            # Standard Norwegian employment contract sections
            sections = [
                ("1. ARBEIDSGIVERS OPPLYSNINGER", [
                    "Bedriftens navn: ________________________________",
                    "Organisasjonsnummer: ____________________________",
                    "Adresse: _______________________________________",
                    "Poststed: ______________________________________"
                ]),
                
                ("2. ARBEIDSTAKERS OPPLYSNINGER", [
                    "Navn: _________________________________________",
                    "Fødselsnummer: _________________________________",
                    "Adresse: _____________________________________",
                    "Poststed: ____________________________________"
                ]),
                
                ("3. ARBEIDSFORHOLD", [
                    "Stillingstittel: _______________________________",
                    "Ansettelsesdato: _______________________________",
                    "Prøvetid: ____________________________________",
                    "Arbeidssted: __________________________________"
                ]),
                
                ("4. ARBEIDSTID", [
                    "Normal arbeidstid: _____ timer per uke",
                    "Arbeidstiden er fordelt på _____ dager per uke",
                    "Overtidsarbeid: _______________________________"
                ]),
                
                ("5. LØNN OG YTELSER", [
                    "Grunnlønn: ___________________________________",
                    "Utbetalingsdato: ______________________________",
                    "Andre ytelser: ________________________________"
                ])
            ]
            
            for section_title, section_items in sections:
                # Section header
                layout.add(Paragraph(
                    section_title,
                    font="Helvetica-Bold",
                    font_size=Decimal(12),
                    font_color=dark_blue,
                    margin_top=Decimal(20),
                    margin_bottom=Decimal(8)
                ))
                
                # Section items
                for item in section_items:
                    layout.add(Paragraph(
                        item,
                        font="Helvetica",
                        font_size=Decimal(10),
                        font_color=black,
                        margin_bottom=Decimal(5)
                    ))
        
        else:
            # Additional pages
            layout.add(Paragraph(
                f"SIDE {page_num + 1}",
                font="Helvetica-Bold",
                font_size=Decimal(14),
                font_color=dark_blue,
                margin_top=Decimal(30),
                margin_bottom=Decimal(20)
            ))
            
            # Additional contract terms
            additional_sections = [
                ("6. FERIE", [
                    "Ferie reguleres av ferieloven",
                    "Feriepenger utbetales: ________________________"
                ]),
                
                ("7. OPPSIGELSE", [
                    "Oppsigelsestid fra arbeidsgiver: _______________",
                    "Oppsigelsestid fra arbeidstaker: _______________"
                ]),
                
                ("8. ØVRIGE BESTEMMELSER", [
                    "Taushetsplikt: ________________________________",
                    "Andre bestemmelser: ____________________________"
                ])
            ]
            
            for section_title, section_items in additional_sections:
                layout.add(Paragraph(
                    section_title,
                    font="Helvetica-Bold",
                    font_size=Decimal(12),
                    font_color=dark_blue,
                    margin_top=Decimal(15),
                    margin_bottom=Decimal(8)
                ))
                
                for item in section_items:
                    layout.add(Paragraph(
                        item,
                        font="Helvetica",
                        font_size=Decimal(10),
                        font_color=black,
                        margin_bottom=Decimal(5)
                    ))
    
    # Add signature section on last page
    layout.add(Paragraph(
        "UNDERSKRIFT",
        font="Helvetica-Bold",
        font_size=Decimal(14),
        font_color=dark_blue,
        margin_top=Decimal(40),
        margin_bottom=Decimal(20)
    ))
    
    # Signature table
    sig_table = FlexibleColumnWidthTable(number_of_columns=2, number_of_rows=3)
    
    sig_table.add(TableCell(Paragraph("ARBEIDSGIVER", font="Helvetica-Bold", font_size=Decimal(10))))
    sig_table.add(TableCell(Paragraph("ARBEIDSTAKER", font="Helvetica-Bold", font_size=Decimal(10))))
    
    sig_table.add(TableCell(Paragraph("Dato: ________________", font="Helvetica", font_size=Decimal(10))))
    sig_table.add(TableCell(Paragraph("Dato: ________________", font="Helvetica", font_size=Decimal(10))))
    
    sig_table.add(TableCell(Paragraph("Underskrift: ________________", font="Helvetica", font_size=Decimal(10))))
    sig_table.add(TableCell(Paragraph("Underskrift: ________________", font="Helvetica", font_size=Decimal(10))))
    
    layout.add(sig_table)
    
    # Save document
    output_path = "recreated_norwegian_contract_from_analysis.pdf"
    with open(output_path, "wb") as pdf_file:
        PDF.dumps(pdf_file, document)
    
    print(f"Norwegian employment contract recreated: {output_path}")
    return output_path

if __name__ == "__main__":
    create_norwegian_employment_contract()
