"""
PDF Analyzer - Extract Structure and Generate Code

Analyzes existing PDF files and generates Python code to recreate them.
Supports form field extraction, layout analysis, and code generation.
"""

import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import json

# PDF Analysis Libraries
try:
    import PyPDF2
    import pdfplumber
    import fitz  # PyMuPDF
    from pdfminer.high_level import extract_text
    from pdfminer.layout import LAParams
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Please install with: pip install PyPDF2 pdfplumber pymupdf pdfminer.six")
    sys.exit(1)

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

from pdf_engine import PDFEngine


class PDFAnalyzer:
    """
    Comprehensive PDF analysis tool that can extract structure,
    form fields, text, and layout information from existing PDFs.
    """
    
    def __init__(self, pdf_path: str):
        """Initialize analyzer with PDF file path."""
        self.pdf_path = Path(pdf_path)
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        self.analysis_results = {}
        
    def analyze_complete(self) -> Dict[str, Any]:
        """
        Perform complete analysis of the PDF file.
        
        Returns:
            Dictionary containing all analysis results
        """
        print(f"Analyzing PDF: {self.pdf_path}")
        
        # Perform all analysis types
        self.analysis_results = {
            'file_info': self._get_file_info(),
            'form_fields': self._extract_form_fields(),
            'text_content': self._extract_text_content(),
            'layout_analysis': self._analyze_layout(),
            'page_structure': self._analyze_page_structure()
        }
        
        return self.analysis_results
    
    def _get_file_info(self) -> Dict[str, Any]:
        """Extract basic file information."""
        try:
            with open(self.pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                
                info = {
                    'num_pages': len(reader.pages),
                    'encrypted': reader.is_encrypted,
                    'metadata': {}
                }
                
                # Extract metadata if available
                if reader.metadata:
                    info['metadata'] = {
                        'title': reader.metadata.get('/Title', ''),
                        'author': reader.metadata.get('/Author', ''),
                        'subject': reader.metadata.get('/Subject', ''),
                        'creator': reader.metadata.get('/Creator', ''),
                        'producer': reader.metadata.get('/Producer', ''),
                        'creation_date': str(reader.metadata.get('/CreationDate', '')),
                        'modification_date': str(reader.metadata.get('/ModDate', ''))
                    }
                
                return info
                
        except Exception as e:
            return {'error': f"Failed to extract file info: {e}"}
    
    def _extract_form_fields(self) -> List[Dict[str, Any]]:
        """Extract interactive form fields from the PDF."""
        form_fields = []
        
        try:
            # Try PyPDF2 first
            with open(self.pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                
                if reader.is_encrypted:
                    print("PDF is encrypted - attempting to decrypt...")
                    try:
                        reader.decrypt('')  # Try empty password
                    except:
                        print("Could not decrypt PDF - form field extraction may be limited")
                
                # Extract form fields from each page
                for page_num, page in enumerate(reader.pages):
                    if '/Annots' in page:
                        annotations = page['/Annots']
                        if annotations:
                            for annot_ref in annotations:
                                try:
                                    annot = annot_ref.get_object()
                                    if annot.get('/Subtype') == '/Widget':
                                        field_info = self._extract_field_info(annot, page_num)
                                        if field_info:
                                            form_fields.append(field_info)
                                except Exception as e:
                                    print(f"Error extracting field: {e}")
            
            # Try PyMuPDF as backup
            if not form_fields:
                form_fields = self._extract_fields_with_pymupdf()
                
        except Exception as e:
            print(f"Error extracting form fields: {e}")
        
        return form_fields
    
    def _extract_field_info(self, annot: Any, page_num: int) -> Optional[Dict[str, Any]]:
        """Extract information from a form field annotation."""
        try:
            field_info = {
                'page': page_num,
                'name': str(annot.get('/T', 'unnamed_field')),
                'type': 'unknown',
                'rect': [],
                'value': '',
                'options': []
            }
            
            # Get field rectangle (position and size)
            if '/Rect' in annot:
                rect = annot['/Rect']
                field_info['rect'] = [float(x) for x in rect]
            
            # Get field value
            if '/V' in annot:
                field_info['value'] = str(annot['/V'])
            
            # Determine field type
            if '/FT' in annot:
                field_type = annot['/FT']
                if field_type == '/Tx':
                    field_info['type'] = 'text'
                elif field_type == '/Btn':
                    field_info['type'] = 'button'  # Could be checkbox or radio
                elif field_type == '/Ch':
                    field_info['type'] = 'choice'  # Dropdown or list
                elif field_type == '/Sig':
                    field_info['type'] = 'signature'
            
            # Get options for choice fields
            if '/Opt' in annot:
                field_info['options'] = [str(opt) for opt in annot['/Opt']]
            
            return field_info
            
        except Exception as e:
            print(f"Error extracting field info: {e}")
            return None
    
    def _extract_fields_with_pymupdf(self) -> List[Dict[str, Any]]:
        """Extract form fields using PyMuPDF as backup method."""
        form_fields = []
        
        try:
            doc = fitz.open(self.pdf_path)
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                widgets = page.widgets()
                
                for widget in widgets:
                    field_info = {
                        'page': page_num,
                        'name': widget.field_name or f'field_{len(form_fields)}',
                        'type': self._map_pymupdf_field_type(widget.field_type),
                        'rect': list(widget.rect),
                        'value': widget.field_value or '',
                        'options': widget.choice_values or []
                    }
                    form_fields.append(field_info)
            
            doc.close()
            
        except Exception as e:
            print(f"Error with PyMuPDF field extraction: {e}")
        
        return form_fields
    
    def _map_pymupdf_field_type(self, field_type: int) -> str:
        """Map PyMuPDF field type constants to readable names."""
        type_map = {
            1: 'text',
            2: 'button',
            3: 'choice',
            4: 'signature'
        }
        return type_map.get(field_type, 'unknown')
    
    def _extract_text_content(self) -> Dict[str, Any]:
        """Extract text content from the PDF."""
        text_content = {
            'full_text': '',
            'pages': []
        }
        
        try:
            # Use pdfplumber for detailed text extraction
            with pdfplumber.open(self.pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text() or ''
                    text_content['pages'].append({
                        'page_number': page_num,
                        'text': page_text,
                        'char_count': len(page_text)
                    })
                    text_content['full_text'] += page_text + '\n'
            
        except Exception as e:
            print(f"Error extracting text: {e}")
            # Fallback to pdfminer
            try:
                text_content['full_text'] = extract_text(str(self.pdf_path))
            except Exception as e2:
                print(f"Fallback text extraction also failed: {e2}")
        
        return text_content
    
    def _analyze_layout(self) -> Dict[str, Any]:
        """Analyze the layout structure of the PDF."""
        layout_info = {
            'pages': [],
            'common_elements': []
        }
        
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_layout = {
                        'page_number': page_num,
                        'width': page.width,
                        'height': page.height,
                        'tables': [],
                        'text_blocks': [],
                        'lines': []
                    }
                    
                    # Extract tables
                    tables = page.find_tables()
                    for table in tables:
                        page_layout['tables'].append({
                            'bbox': table.bbox,
                            'rows': len(table.rows) if table.rows else 0,
                            'cols': len(table.rows[0]) if table.rows and table.rows[0] else 0
                        })
                    
                    # Extract text blocks
                    chars = page.chars
                    if chars:
                        # Group characters into text blocks (simplified)
                        current_block = []
                        for char in chars[:50]:  # Limit for performance
                            current_block.append({
                                'text': char.get('text', ''),
                                'x0': char.get('x0', 0),
                                'y0': char.get('y0', 0),
                                'fontname': char.get('fontname', ''),
                                'size': char.get('size', 0)
                            })
                        page_layout['text_blocks'] = current_block
                    
                    layout_info['pages'].append(page_layout)
            
        except Exception as e:
            print(f"Error analyzing layout: {e}")
        
        return layout_info
    
    def _analyze_page_structure(self) -> Dict[str, Any]:
        """Analyze the overall page structure and common patterns."""
        structure = {
            'page_sizes': [],
            'common_fonts': {},
            'text_patterns': []
        }
        
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                for page in pdf.pages:
                    structure['page_sizes'].append({
                        'width': page.width,
                        'height': page.height
                    })
                    
                    # Analyze fonts
                    chars = page.chars
                    for char in chars[:100]:  # Limit for performance
                        font = char.get('fontname', 'unknown')
                        size = char.get('size', 0)
                        font_key = f"{font}_{size}"
                        structure['common_fonts'][font_key] = structure['common_fonts'].get(font_key, 0) + 1
            
        except Exception as e:
            print(f"Error analyzing page structure: {e}")
        
        return structure
    
    def generate_code(self, output_file: str = "recreated_pdf.py") -> str:
        """
        Generate Python code to recreate the PDF using our PDF engine.
        
        Args:
            output_file: Name of the output Python file
            
        Returns:
            Generated Python code as string
        """
        if not self.analysis_results:
            self.analyze_complete()
        
        code_lines = [
            '"""',
            'Generated PDF Recreation Code',
            f'Source: {self.pdf_path.name}',
            f'Generated by PDF Analyzer',
            '"""',
            '',
            'import sys',
            'from pathlib import Path',
            '',
            '# Add parent directory to path for imports',
            'sys.path.append(str(Path(__file__).parent))',
            '',
            'from pdf_engine import PDFEngine',
            '',
            '',
            'def recreate_pdf():',
            '    """Recreate the analyzed PDF using PDF Engine."""',
            '    ',
            '    # Initialize the PDF engine',
            '    engine = PDFEngine()',
            '    '
        ]
        
        # Generate code based on analysis results
        form_fields = self.analysis_results.get('form_fields', [])
        text_content = self.analysis_results.get('text_content', {})
        
        if form_fields:
            # Generate form creation code
            code_lines.extend([
                '    # Create interactive form',
                '    form = engine.create_form("recreated_form.pdf")',
                '    '
            ])
            
            for field in form_fields:
                field_code = self._generate_field_code(field)
                if field_code:
                    code_lines.append(f'    {field_code}')
            
            code_lines.extend([
                '    ',
                '    # Render the form',
                '    form.render_to_file("recreated_form.pdf")',
                '    print("Interactive form created: recreated_form.pdf")'
            ])
        
        else:
            # Generate document creation code
            code_lines.extend([
                '    # Create static document',
                '    doc = engine.create_document("recreated_document.pdf")',
                '    '
            ])
            
            # Add text content if available
            pages = text_content.get('pages', [])
            for i, page in enumerate(pages[:3]):  # Limit to first 3 pages
                text = page.get('text', '').strip()
                if text:
                    # Split into paragraphs and add them
                    paragraphs = text.split('\n\n')
                    for j, paragraph in enumerate(paragraphs[:5]):  # Limit paragraphs
                        if paragraph.strip():
                            clean_text = paragraph.replace('"', '\\"').replace('\n', ' ')[:200]
                            if j == 0 and i == 0:
                                code_lines.append(f'    doc.add_title("{clean_text[:50]}...")')
                            else:
                                code_lines.append(f'    doc.add_paragraph("{clean_text}...")')
            
            code_lines.extend([
                '    ',
                '    # Render the document',
                '    doc.render_to_file("recreated_document.pdf")',
                '    print("Static document created: recreated_document.pdf")'
            ])
        
        code_lines.extend([
            '',
            '',
            'if __name__ == "__main__":',
            '    recreate_pdf()'
        ])
        
        # Write code to file
        code_content = '\n'.join(code_lines)
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(code_content)
        
        print(f"Generated code saved to: {output_file}")
        return code_content
    
    def _generate_field_code(self, field: Dict[str, Any]) -> str:
        """Generate code for a specific form field."""
        field_type = field.get('type', 'text')
        name = field.get('name', 'unnamed')
        rect = field.get('rect', [0, 0, 100, 20])
        value = field.get('value', '')
        
        if len(rect) >= 4:
            x, y, x2, y2 = rect[:4]
            width = abs(x2 - x)
            height = abs(y2 - y)
        else:
            x, y, width, height = 100, 100, 200, 25
        
        if field_type == 'text':
            return f'form.add_text_field("{name}", x={x:.1f}, y={y:.1f}, width={width:.1f}, height={height:.1f}, default_value="{value}")'
        elif field_type == 'button':
            return f'form.add_checkbox("{name}", x={x:.1f}, y={y:.1f}, size={min(width, height):.1f}, checked={value.lower() == "yes"})'
        elif field_type == 'signature':
            return f'form.add_signature_field("{name}", x={x:.1f}, y={y:.1f}, width={width:.1f}, height={height:.1f})'
        else:
            return f'# Unknown field type: {field_type} - {name}'
    
    def save_analysis(self, output_file: str = "pdf_analysis.json"):
        """Save analysis results to JSON file."""
        if not self.analysis_results:
            self.analyze_complete()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, default=str)
        
        print(f"Analysis results saved to: {output_file}")


def main():
    """Main function for command-line usage."""
    if len(sys.argv) < 2:
        print("Usage: python pdf_analyzer.py <pdf_file_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    try:
        analyzer = PDFAnalyzer(pdf_path)
        
        print("Starting PDF analysis...")
        results = analyzer.analyze_complete()
        
        print(f"\n=== Analysis Results ===")
        print(f"File: {pdf_path}")
        print(f"Pages: {results['file_info'].get('num_pages', 'unknown')}")
        print(f"Encrypted: {results['file_info'].get('encrypted', 'unknown')}")
        print(f"Form fields found: {len(results['form_fields'])}")
        
        # Save analysis results
        analyzer.save_analysis()
        
        # Generate recreation code
        code = analyzer.generate_code()
        
        print(f"\n=== Form Fields Found ===")
        for field in results['form_fields']:
            print(f"- {field['name']} ({field['type']}) on page {field['page']}")
        
        print(f"\n=== Generated Files ===")
        print("- pdf_analysis.json (detailed analysis)")
        print("- recreated_pdf.py (recreation code)")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
