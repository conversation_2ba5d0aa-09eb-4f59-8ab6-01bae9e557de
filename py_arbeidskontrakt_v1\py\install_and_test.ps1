# PDF Engine Installation and Test Script
# PowerShell script to set up the PDF Engine and run examples

Write-Host "=== PDF Engine Setup and Test ===" -ForegroundColor Green

# Check if virtual environment exists
if (-not (Test-Path "venv")) {
    Write-Host "Creating virtual environment..." -ForegroundColor Yellow
    python -m venv venv
}

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"

# Upgrade pip
Write-Host "Upgrading pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip

# Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Yellow
pip install -r requirements.txt

# Verify installation
Write-Host "Verifying ReportLab installation..." -ForegroundColor Yellow
python -c "import reportlab; print(f'ReportLab version: {reportlab.Version}')"

# Create output directory
if (-not (Test-Path "output")) {
    New-Item -ItemType Directory -Path "output"
}

# Run examples
Write-Host "Running basic document example..." -ForegroundColor Yellow
Set-Location examples
python basic_document.py

Write-Host "Running interactive form example..." -ForegroundColor Yellow
python interactive_form.py

Write-Host "Running arbeidskontrakt generator..." -ForegroundColor Yellow
python arbeidskontrakt_generator.py

# Move generated files to output directory
Write-Host "Moving generated files to output directory..." -ForegroundColor Yellow
Set-Location ..
Move-Item "examples\*.pdf" "output\" -Force -ErrorAction SilentlyContinue

# Test PDF analysis tools
Write-Host "Testing PDF analysis tools..." -ForegroundColor Yellow
if (Test-Path "../Norwegian - bokmal - Standard contract of employment.pdf") {
    Write-Host "Analyzing Norwegian employment contract..." -ForegroundColor Cyan
    python analyze_norwegian_contract.py
    Move-Item "*.pdf" "output\" -Force -ErrorAction SilentlyContinue
    Move-Item "*.json" "output\" -Force -ErrorAction SilentlyContinue
    Move-Item "*.py" "output\" -Force -ErrorAction SilentlyContinue -Exclude "pdf_analyzer.py", "analyze_norwegian_contract.py"
} else {
    Write-Host "Norwegian contract PDF not found - skipping analysis" -ForegroundColor Yellow
}

# List generated files
Write-Host "Generated PDF files:" -ForegroundColor Green
Get-ChildItem "output\*.pdf" | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor Cyan }

Write-Host "`n=== Setup Complete ===" -ForegroundColor Green
Write-Host "PDF Engine is ready to use!" -ForegroundColor Green
Write-Host "Check the 'output' directory for generated PDF files." -ForegroundColor Yellow

# Optional: Open output directory
$openDir = Read-Host "`nOpen output directory? (y/n)"
if ($openDir -eq "y" -or $openDir -eq "Y") {
    Invoke-Item "output"
}
