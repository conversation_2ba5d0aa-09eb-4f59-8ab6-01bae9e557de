#!/usr/bin/env python3
"""
Arbeidskontrakt Generator for Ringerike Landskap AS
Generates employment contracts as DOCX files based on employee data.
"""

import argparse
import sys
from datetime import datetime
from pathlib import Path

try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
except ImportError:
    print("Error: python-docx library not installed.")
    print("Please run: pip install python-docx")
    sys.exit(1)


class Arbeidskontrakt:
    """Employment contract generator for Ringerike Landskap AS"""

    def __init__(self):
        self.company_info = {
            "name": "Ringerike Landskap AS",
            "org_nr": "***********",
            "address": "Birchs vei 7, 3530 Røyse"
        }

    def create_contract(self, employee_data, output_path="arbeidskontrakt.docx"):
        """
        Create employment contract DOCX file

        Args:
            employee_data (dict): Employee information
            output_path (str): Output file path
        """
        doc = Document()

        # Set document margins for A4 page
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(0.8)
            section.bottom_margin = Inches(0.8)
            section.left_margin = Inches(0.8)
            section.right_margin = Inches(0.8)

        # Title
        title = doc.add_heading('ARBEIDSAVTALE', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Company info
        company_para = doc.add_paragraph()
        company_para.add_run(f"Arbeidsgiver: ").bold = True
        company_para.add_run(f"{self.company_info['name']} • Org.nr: {self.company_info['org_nr']}")
        company_para.add_run(f"\n{self.company_info['address']}")

        # Employee info
        employee_para = doc.add_paragraph()
        employee_para.add_run("Arbeidstaker: ").bold = True
        employee_para.add_run(f"Navn: {employee_data.get('navn', '_' * 30)}")
        employee_para.add_run(f"\nAdresse: {employee_data.get('adresse', '_' * 25)}")
        employee_para.add_run(f"\nFødselsdato: {employee_data.get('fodselsdato', '_' * 20)}")

        doc.add_paragraph("─" * 60)

        # 1. Employment relationship
        self._add_section(doc, "1. ANSETTELSESFORHOLD", [
            f"Startdato: {employee_data.get('startdato', '_' * 15)}",
            f"☐ Fast  ☐ Midlertidig t.o.m. {employee_data.get('sluttdato', '_' * 10)} (grunn: {employee_data.get('midlertidig_grunn', '_' * 15)})",
            f"☐ Prøvetid {employee_data.get('provetid', '_')} mnd ☐ Ingen prøvetid"
        ])

        # 2. Workplace
        self._add_section(doc, "2. ARBEIDSSTED", [
            "Ringerike og omegn, oppmøtested etter prosjekt."
        ])

        # 3. Position and tasks
        self._add_section(doc, "3. STILLING OG OPPGAVER", [
            f"Stilling: {employee_data.get('stilling', '_' * 30)}",
            "Utfører anleggsgartner- og grunnarbeid inkl. øvrige oppgaver innen virksomheten."
        ])

        # 4. Working hours
        self._add_section(doc, "4. ARBEIDSTID", [
            "37,5 t/uke, normalt kl. 07–15. Pauser og fleksibilitet ihht. AML."
        ])

        # 5. Salary and compensation
        self._add_section(doc, "5. LØNN OG GODTGJØRELSE", [
            f"Kr {employee_data.get('timelonn', '300')},-/time. Utbetaling den 5. hver måned.",
            "Overtid/helgetillegg etter AML § 10-6. Kjøring etter statens satser."
        ])

        # 6. Vacation and vacation pay
        self._add_section(doc, "6. FERIE OG FERIEPENGER", [
            "5 uker ferie iht. ferieloven. Feriepenger 12 %.",
            "Utbetales ved fratreden ved kort ansettelse."
        ])

        # 7. Termination
        self._add_section(doc, "7. OPPSIGELSE", [
            "14 dager i prøvetid, 1 måned etter prøvetid."
        ])

        # 8. Miscellaneous
        self._add_section(doc, "8. DIVERSE", [
            "Arbeidstaker følger instrukser, arbeidsgiver leverer arbeidstøy/verktøy.",
            "Ingen tariffavtale pr. dato."
        ])

        doc.add_paragraph("─" * 60)

        # Signatures
        sig_para = doc.add_paragraph()
        sig_para.add_run("Sted/dato: ").bold = True
        sig_para.add_run(f"Røyse, {datetime.now().strftime('%d.%m.%Y')}")

        doc.add_paragraph()

        sig_table = doc.add_table(rows=2, cols=2)
        sig_table.cell(0, 0).text = "Arbeidsgiver:"
        sig_table.cell(0, 1).text = "Arbeidstaker:"
        sig_table.cell(1, 0).text = "_" * 25
        sig_table.cell(1, 1).text = "_" * 25

        # Save document
        doc.save(output_path)
        print(f"Arbeidskontrakt saved to: {output_path}")

    def _add_section(self, doc, title, content_lines):
        """Add a section with title and content"""
        heading = doc.add_heading(title, level=3)
        for line in content_lines:
            doc.add_paragraph(line)


def get_employee_data_interactive():
    """Get employee data through interactive prompts"""
    print("=== Arbeidskontrakt Generator ===")
    print("Enter employee information (press Enter to skip optional fields):\n")

    data = {}

    # Required fields
    data['navn'] = input("Navn (required): ").strip()
    if not data['navn']:
        print("Error: Name is required")
        return None

    # Optional fields
    data['adresse'] = input("Adresse: ").strip()
    data['fodselsdato'] = input("Fødselsdato (dd.mm.yyyy): ").strip()
    data['startdato'] = input("Startdato (dd.mm.yyyy): ").strip()
    data['stilling'] = input("Stillingstittel: ").strip()
    data['timelonn'] = input("Timelønn (default 300): ").strip() or "300"

    # Employment type
    print("\nAnsettelsestype:")
    print("1. Fast ansettelse")
    print("2. Midlertidig ansettelse")
    choice = input("Velg (1/2): ").strip()

    if choice == "2":
        data['sluttdato'] = input("Sluttdato (dd.mm.yyyy): ").strip()
        data['midlertidig_grunn'] = input("Grunn for midlertidig ansettelse: ").strip()

    # Probation period
    provetid = input("Prøvetid i måneder (0-6, blank for ingen): ").strip()
    if provetid and provetid.isdigit() and 0 < int(provetid) <= 6:
        data['provetid'] = provetid

    return data


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Generate employment contracts for Ringerike Landskap AS")
    parser.add_argument("--prompt", action="store_true", help="Interactive mode with prompts")
    parser.add_argument("--output", "-o", default="arbeidskontrakt.docx", help="Output file path")

    args = parser.parse_args()

    generator = Arbeidskontrakt()

    if args.prompt:
        employee_data = get_employee_data_interactive()
        if employee_data is None:
            return 1
    else:
        # Example data for testing
        employee_data = {
            'navn': 'Ola Nordmann',
            'adresse': 'Testveien 123, 1234 Testby',
            'fodselsdato': '01.01.1990',
            'startdato': '01.05.2025',
            'stilling': 'Anleggsgartner',
            'timelonn': '300'
        }
        print("Using example data. Use --prompt for interactive mode.")

    try:
        generator.create_contract(employee_data, args.output)
        print(f"\nSuccess! Contract generated: {args.output}")
        return 0
    except Exception as e:
        print(f"Error generating contract: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())