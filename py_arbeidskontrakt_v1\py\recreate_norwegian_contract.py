"""
Norwegian Employment Contract Recreation
Customized for Ringerike Landskap AS
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

from pdf_engine import PDFEngine


def create_norwegian_contract():
    """Create a Norwegian employment contract based on the official template."""
    
    # Initialize the PDF engine
    engine = PDFEngine()
    
    # Create interactive form based on Norwegian template
    form = engine.create_form("norwegian_contract_interactive.pdf")
    
    # Add form fields based on analysis
    # Text input fields
    form.add_text_field("employee_name", x=43.8, y=701.2, width=505.3, height=15.9, default_value="")
    form.add_text_field("virksomhet_-_orgnr", x=43.8, y=672.3, width=505.3, height=15.9, default_value="")
    form.add_text_field("address", x=43.8, y=644.2, width=505.3, height=15.9, default_value="")
    form.add_text_field("employee_name", x=43.8, y=591.4, width=364.8, height=15.9, default_value="")
    form.add_text_field("date", x=414.0, y=591.4, width=137.1, height=15.9, default_value="")
    form.add_text_field("address", x=43.8, y=563.2, width=505.3, height=15.9, default_value="")
    form.add_text_field("address", x=43.8, y=510.7, width=505.3, height=15.9, default_value="")
    form.add_text_field("ansatt_som_-_tittel", x=43.8, y=438.8, width=505.3, height=36.2, default_value="")
    form.add_text_field("arbeidsforhold_-_forventet_varighet", x=186.5, y=328.3, width=363.9, height=15.9, default_value="")
    form.add_text_field("arbeidsforhold_-_grunn_til_ansettelse", x=186.5, y=278.8, width=363.9, height=36.3, default_value="")
    form.add_text_field("arbeidsforhold_-_ansatt_fra", x=43.8, y=387.9, width=250.1, height=15.9, default_value="")
    form.add_text_field("position", x=300.1, y=387.9, width=250.3, height=15.9, default_value="")
    form.add_text_field("arbeidsforhold_-_ukentlig_arbeidstid", x=43.8, y=252.6, width=505.3, height=15.9, default_value="")
    form.add_text_field("arbeidsforhold_-_daglig_arbeidstid", x=43.8, y=224.0, width=505.3, height=15.9, default_value="")
    form.add_text_field("arbeidsforhold_-_arbeidstidens_plassering", x=43.8, y=176.8, width=505.3, height=34.9, default_value="")
    form.add_text_field("arbeidsforhold_-_periode_tidspunkt", x=43.8, y=131.4, width=505.3, height=34.9, default_value="")
    form.add_text_field("arbeidsforhold_-_antall_og_lengde", x=43.8, y=105.2, width=505.3, height=15.9, default_value="")
    form.add_text_field("arbeidsforhold_-_ev_særlig_arbtidordning", x=43.8, y=57.6, width=505.3, height=34.9, default_value="")
    form.add_text_field("arbeidsforhold_-_ordninger_endre_vakter", x=44.5, y=750.8, width=505.9, height=34.9, default_value="")
    form.add_text_field("arbeidsforhold_-_ordninger_utover", x=44.5, y=705.3, width=505.9, height=34.9, default_value="")
    form.add_text_field("arbeidsforhold_-_arbtaker_oppsigelsesfrist", x=44.5, y=660.0, width=505.9, height=34.9, default_value="")
    form.add_text_field("arbeidsforhold_-_arbgiver_oppsigelsesfrist", x=44.5, y=614.6, width=505.9, height=34.9, default_value="")
    form.add_text_field("arbeidsforhold_-_ferietid", x=44.5, y=569.2, width=505.9, height=34.9, default_value="")
    form.add_text_field("arbeidsforhold_-_rett_til_fravær", x=44.5, y=506.7, width=505.9, height=52.2, default_value="")
    form.add_text_field("eventuell_prøvetid_-_prøvetidens_lengde", x=44.5, y=456.2, width=505.9, height=15.9, default_value="")
    form.add_text_field("eventuell_prøvetid_-_oppsigelsesfrist", x=44.5, y=427.9, width=505.9, height=15.9, default_value="")
    form.add_text_field("eventuell_prøvetid_-_forlengelse", x=44.5, y=399.6, width=505.9, height=15.9, default_value="")
    form.add_text_field("salary", x=44.5, y=347.1, width=505.9, height=15.9, default_value="")
    form.add_text_field("salary", x=44.5, y=318.5, width=505.9, height=15.9, default_value="")
    form.add_text_field("salary", x=44.5, y=290.3, width=505.9, height=15.9, default_value="")
    form.add_text_field("salary", x=44.5, y=261.8, width=505.9, height=15.9, default_value="")
    form.add_text_field("salary", x=44.5, y=233.5, width=505.9, height=15.9, default_value="")
    form.add_text_field("salary", x=44.5, y=205.2, width=505.9, height=15.9, default_value="")
    form.add_text_field("salary", x=44.5, y=176.9, width=505.9, height=15.9, default_value="")
    form.add_text_field("salary", x=44.5, y=129.5, width=505.9, height=34.9, default_value="")
    form.add_text_field("pkt8_tariffavtaleb", x=44.6, y=750.7, width=505.9, height=34.9, default_value="")
    form.add_text_field("pkt9_rett_til_kompetanseutvikling", x=44.6, y=647.0, width=505.9, height=70.2, default_value="")
    form.add_text_field("pkt10_ytelser", x=44.6, y=543.1, width=505.9, height=70.2, default_value="")
    form.add_text_field("pkt11_innleiers_identitet", x=44.6, y=473.7, width=505.9, height=34.9, default_value="")
    form.add_text_field("pkt12_andre_opplysninger", x=44.6, y=370.0, width=505.9, height=78.9, default_value="")
    form.add_text_field("date", x=44.6, y=319.6, width=505.9, height=15.9, default_value="")
    form.add_text_field("underskrifter_-_for_arbeidsgiver", x=44.6, y=274.4, width=505.9, height=31.5, default_value="")
    form.add_text_field("underskrifter_-_arbeidsgiver", x=44.6, y=229.0, width=505.9, height=31.5, default_value="")
    form.add_text_field("underskrifter_-_arbeidstaker", x=44.6, y=183.8, width=505.9, height=31.5, default_value="")
    
    # Render the interactive form
    form.render_to_file("norwegian_contract_interactive.pdf")
    print("Interactive Norwegian contract created: norwegian_contract_interactive.pdf")
    
    # Also create a static version with Ringerike Landskap details
    create_static_version(engine)


def create_static_version(engine):
    """Create a static version pre-filled for Ringerike Landskap AS."""
    
    doc = engine.create_document("norwegian_contract_ringerike.pdf")
    
    # Add contract header
    doc.add_title("ARBEIDSAVTALE") \
       .add_spacer(10) \
       .add_paragraph("Arbeidsgiver: Ringerike Landskap AS") \
       .add_paragraph("Organisasjonsnummer: 924 826 541") \
       .add_paragraph("Adresse: c/o Kim Tuvsjøen, Birchs vei 7, 3530 Røyse") \
       .add_spacer(15)
    
    # Employee information section
    doc.add_paragraph("Arbeidstaker: ________________________________") \
       .add_paragraph("Fødselsnummer: ________________________________") \
       .add_paragraph("Adresse: ________________________________") \
       .add_spacer(20)
    
    # Contract sections based on Norwegian requirements
    sections = [
        ("1. ARBEIDSSTED", 
         "Arbeidstakeren skal arbeide ved Ringerike Landskap AS med hovedarbeidssted i Røyse kommune. "
         "Arbeidstakeren kan også utføre arbeid på ulike prosjektsteder i Ringerike og omkringliggende kommuner."),
        
        ("2. STILLINGSBESKRIVELSE",
         "Arbeidstakeren ansettes som anleggsgartnerfagarbeider og skal utføre arbeidsoppgaver innenfor "
         "anleggsgartnerfaget, inkludert anleggsarbeid, hagearbeid, graving og terrengarbeid."),
        
        ("3. ANSETTELSESFORHOLD",
         "Oppstart: ________________________________\n"
         "Ansettelsestype: ☐ Fast ansettelse ☐ Midlertidig ansettelse\n"
         "Prøvetid: ☐ Ingen prøvetid ☐ _____ måneder (maks 6 måneder)"),
        
        ("4. ARBEIDSTID",
         "Ordinær arbeidstid: 37,5 timer per uke\n"
         "Arbeidstidsordning: Dagarbeid, mandag til fredag\n"
         "Arbeidstid: 07:00 - 15:30 (inkludert 30 min lunsj)"),
        
        ("5. LØNN OG GODTGJØRELSER",
         "Grunnlønn: Kr ________________________________ per måned\n"
         "Lønnsutbetaling: Den 5. i hver måned\n"
         "Kilometergodtgjørelse ved bruk av privat bil: Kr 300,- per måned"),
        
        ("6. FERIE",
         "Arbeidstakeren har rett til 5 ukers ferie per år i henhold til ferieloven. "
         "Feriepenger utbetales med 12% av opptjent lønn."),
    ]
    
    # Add all sections
    for title, content in sections:
        doc.add_title(title, style="Heading2") \
           .add_paragraph(content) \
           .add_spacer(15)
    
    # Add signature section
    doc.add_spacer(30) \
       .add_paragraph("Dato: ________________________________") \
       .add_spacer(20) \
       .add_paragraph("Arbeidsgiver: Ringerike Landskap AS") \
       .add_spacer(30) \
       .add_paragraph("________________________________") \
       .add_paragraph("Underskrift arbeidsgiver") \
       .add_spacer(30) \
       .add_paragraph("________________________________") \
       .add_paragraph("Underskrift arbeidstaker")
    
    # Render the document
    doc.render_to_file("norwegian_contract_ringerike.pdf")
    print("Static Norwegian contract created: norwegian_contract_ringerike.pdf")


if __name__ == "__main__":
    create_norwegian_contract()