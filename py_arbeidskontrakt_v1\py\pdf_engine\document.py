"""
Document Builder Module

Handles static PDF document creation with support for text, images,
tables, and complex layouts. Provides clean abstraction over underlying
PDF generation library.
"""

from typing import Optional, Dict, Any, Union, List, Tuple
from pathlib import Path
import io

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import inch, cm
from reportlab.lib.colors import black, white
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle

from .exceptions import ValidationError, RenderError


class DocumentBuilder:
    """
    Builder for static PDF documents.
    
    Provides high-level interface for creating professional documents
    with text, images, tables, and custom layouts.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize document builder with configuration."""
        self.config = config
        self._documents = {}
        self._current_doc = None
        
    def new_document(self, 
                    output_path: Optional[Union[str, Path]] = None,
                    page_size: Tuple[float, float] = A4,
                    margins: Optional[Dict[str, float]] = None) -> 'DocumentInstance':
        """
        Create a new document instance.
        
        Args:
            output_path: Optional output file path
            page_size: Page size tuple (width, height)
            margins: Optional margins dict with keys: top, bottom, left, right
            
        Returns:
            DocumentInstance for method chaining
        """
        doc_id = id(self)
        margins = margins or {'top': 2*cm, 'bottom': 2*cm, 'left': 2*cm, 'right': 2*cm}
        
        doc_instance = DocumentInstance(
            output_path=output_path,
            page_size=page_size,
            margins=margins,
            config=self.config
        )
        
        self._documents[doc_id] = doc_instance
        self._current_doc = doc_instance
        return doc_instance


class DocumentInstance:
    """
    Individual document instance with fluent interface.
    
    Supports method chaining for building documents step by step.
    """
    
    def __init__(self, 
                 output_path: Optional[Union[str, Path]],
                 page_size: Tuple[float, float],
                 margins: Dict[str, float],
                 config: Dict[str, Any]):
        """Initialize document instance."""
        self.output_path = output_path
        self.page_size = page_size
        self.margins = margins
        self.config = config
        self.elements = []
        self.styles = getSampleStyleSheet()
        
    def add_title(self, text: str, style: Optional[str] = None) -> 'DocumentInstance':
        """
        Add a title to the document.
        
        Args:
            text: Title text
            style: Optional style name
            
        Returns:
            Self for method chaining
        """
        if not text:
            raise ValidationError("Title text cannot be empty")
            
        style_name = style or 'Title'
        paragraph = Paragraph(text, self.styles[style_name])
        self.elements.append(paragraph)
        self.elements.append(Spacer(1, 12))
        return self
    
    def add_paragraph(self, text: str, style: Optional[str] = None) -> 'DocumentInstance':
        """
        Add a paragraph to the document.
        
        Args:
            text: Paragraph text
            style: Optional style name
            
        Returns:
            Self for method chaining
        """
        if not text:
            raise ValidationError("Paragraph text cannot be empty")
            
        style_name = style or 'Normal'
        paragraph = Paragraph(text, self.styles[style_name])
        self.elements.append(paragraph)
        self.elements.append(Spacer(1, 6))
        return self
    
    def add_table(self, 
                  data: List[List[str]], 
                  headers: Optional[List[str]] = None,
                  style: Optional[List[Tuple]] = None) -> 'DocumentInstance':
        """
        Add a table to the document.
        
        Args:
            data: Table data as list of rows
            headers: Optional column headers
            style: Optional table style commands
            
        Returns:
            Self for method chaining
        """
        if not data:
            raise ValidationError("Table data cannot be empty")
            
        table_data = data
        if headers:
            table_data = [headers] + data
            
        table = Table(table_data)
        
        # Apply default or custom style
        if style:
            table.setStyle(TableStyle(style))
        else:
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), '#f0f0f0'),
                ('TEXTCOLOR', (0, 0), (-1, 0), black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), white),
                ('GRID', (0, 0), (-1, -1), 1, black)
            ]))
            
        self.elements.append(table)
        self.elements.append(Spacer(1, 12))
        return self
    
    def add_spacer(self, height: float = 12) -> 'DocumentInstance':
        """
        Add vertical space to the document.

        Args:
            height: Space height in points

        Returns:
            Self for method chaining
        """
        self.elements.append(Spacer(1, height))
        return self

    def add_page_break(self) -> 'DocumentInstance':
        """
        Add a page break to the document.

        Returns:
            Self for method chaining
        """
        from reportlab.platypus import PageBreak
        self.elements.append(PageBreak())
        return self
    
    def render_to_bytes(self) -> bytes:
        """
        Render document to bytes.
        
        Returns:
            PDF content as bytes
        """
        try:
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=self.page_size,
                topMargin=self.margins['top'],
                bottomMargin=self.margins['bottom'],
                leftMargin=self.margins['left'],
                rightMargin=self.margins['right']
            )
            doc.build(self.elements)
            return buffer.getvalue()
        except Exception as e:
            raise RenderError(f"Failed to render document: {e}") from e
    
    def render_to_file(self, output_path: Union[str, Path]) -> None:
        """
        Render document to file.
        
        Args:
            output_path: Output file path
        """
        try:
            doc = SimpleDocTemplate(
                str(output_path),
                pagesize=self.page_size,
                topMargin=self.margins['top'],
                bottomMargin=self.margins['bottom'],
                leftMargin=self.margins['left'],
                rightMargin=self.margins['right']
            )
            doc.build(self.elements)
        except Exception as e:
            raise RenderError(f"Failed to save document to {output_path}: {e}") from e
