#!/usr/bin/env python3
"""
PDF Creation Example using borb
Based on analysis of: py/Arbeidskontrakt_Ringerike_Landskap.pdf
"""

from decimal import Decimal
from borb.pdf import Document, Page, PDF, SingleColumnLayout, Paragraph
from borb.pdf.canvas.color.color import Hex<PERSON><PERSON>r

def create_employment_contract():
    """Create an employment contract PDF"""
    # Create new document
    document = Document()
    page = Page()
    document.add_page(page)
    layout = SingleColumnLayout(page)
    
    # Define styling
    primary_color = HexColor("#2c3e50")
    text_color = HexColor("#2c3e50")
    
    # Title
    layout.add(Paragraph(
        "ARBEIDSKONTRAKT",
        font="Helvetica-Bold",
        font_size=Decimal(20),
        font_color=primary_color,
        margin_top=Decimal(20),
        margin_bottom=Decimal(20)
    ))
    
    # Company info
    layout.add(Paragraph(
        "Ringerike Landskap AS",
        font="Helvetica-Bold",
        font_size=Decimal(14),
        font_color=primary_color,
        margin_bottom=Decimal(10)
    ))
    
    # Contract sections
    sections = [
        ("1. ARBEIDSGIVERS OPPLYSNINGER", "Ringerike Landskap AS\nOrg.nr: [ORGANISASJONSNUMMER]"),
        ("2. ARBEIDSTAKERS OPPLYSNINGER", "Navn: [NAVN]\nFødselsnummer: [FØDSELSNUMMER]"),
        ("3. ARBEIDSFORHOLD", "Stillingstittel: Anleggsgartner\nAnsettelsesdato: [DATO]"),
        ("4. ARBEIDSSTED", "Arbeidsstedet er: [ADRESSE]"),
        ("5. ARBEIDSTID", "Normal arbeidstid er 37,5 timer per uke"),
        ("6. LØNN", "Lønn utbetales månedlig"),
        ("7. FERIE", "Ferie reguleres av ferieloven"),
        ("8. OPPSIGELSE", "Oppsigelsestid i henhold til arbeidsmiljøloven")
    ]
    
    for title, content in sections:
        # Section title
        layout.add(Paragraph(
            title,
            font="Helvetica-Bold",
            font_size=Decimal(12),
            font_color=primary_color,
            margin_top=Decimal(15),
            margin_bottom=Decimal(5)
        ))
        
        # Section content
        layout.add(Paragraph(
            content,
            font="Helvetica",
            font_size=Decimal(11),
            font_color=text_color,
            margin_bottom=Decimal(10)
        ))
    
    # Signature section
    layout.add(Paragraph(
        "UNDERSKRIFT",
        font="Helvetica-Bold",
        font_size=Decimal(12),
        font_color=primary_color,
        margin_top=Decimal(30),
        margin_bottom=Decimal(20)
    ))
    
    layout.add(Paragraph(
        "Dato: ________________    Arbeidsgiver: ________________",
        font="Helvetica",
        font_size=Decimal(11),
        margin_bottom=Decimal(15)
    ))
    
    layout.add(Paragraph(
        "Dato: ________________    Arbeidstaker: ________________",
        font="Helvetica",
        font_size=Decimal(11),
        margin_bottom=Decimal(20)
    ))
    
    # Save the document
    output_path = "generated_employment_contract.pdf"
    with open(output_path, "wb") as pdf_file:
        PDF.dumps(pdf_file, document)
    
    print(f"Employment contract created: {output_path}")
    return output_path

if __name__ == "__main__":
    create_employment_contract()
