#!/usr/bin/env python3
"""
PDF Recreation Code
Generated from: Norwegian - bokmal - Standard contract of employment-unlocked.pdf
Based on extracted structure and content
"""

from decimal import Decimal
from borb.pdf import Document, Page, PDF, SingleColumnLayout, Paragraph
from borb.pdf.canvas.color.color import HexColor
from borb.pdf.canvas.layout.table import Table, TableCell, FlexibleColumnWidthTable

def recreate_norwegian_employment_contract():
    """Recreate the Norwegian employment contract PDF"""
    
    # Create document
    document = Document()
    
    # Define styling
    primary_color = HexColor("#000000")  # Black for official documents
    text_color = HexColor("#000000")
    
    # Page dimensions from original: 595.0 x 842.0 pts
    
    # Create pages based on original structure
    for page_num in range(5):
        page = Page()
        document.add_page(page)
        layout = SingleColumnLayout(page)
        
        if page_num == 0:
            # First page - Title and main content
            layout.add(Paragraph(
                "ARBEIDSAVTALE",
                font="Helvetica-Bold",
                font_size=Decimal(16),
                font_color=primary_color,
                margin_top=Decimal(20),
                margin_bottom=Decimal(20)
            ))
            
            # Add sections based on typical Norwegian employment contract
            sections = [
                ("1. ARBEIDSGIVERS OPPLYSNINGER", 
                 "Navn: [BEDRIFTSNAVN]\n"
                 "Organisasjonsnummer: [ORG.NR]\n"
                 "Adresse: [ADRESSE]"),
                
                ("2. ARBEIDSTAKERS OPPLYSNINGER",
                 "Navn: [NAVN]\n"
                 "Fødselsnummer: [FØDSELSNUMMER]\n"
                 "Adresse: [ADRESSE]"),
                
                ("3. ARBEIDSFORHOLD",
                 "Stillingstittel: [STILLING]\n"
                 "Ansettelsesdato: [DATO]\n"
                 "Prøvetid: [PRØVETID]"),
                
                ("4. ARBEIDSSTED",
                 "Arbeidsstedet er: [ARBEIDSSTED]\n"
                 "Arbeidsgiver kan endre arbeidsstedet innenfor [OMRÅDE]"),
                
                ("5. ARBEIDSTID",
                 "Normal arbeidstid er [TIMER] timer per uke\n"
                 "Arbeidstiden er fordelt på [DAGER] dager per uke"),
                
                ("6. LØNN OG YTELSER",
                 "Lønn: [LØNN] per [PERIODE]\n"
                 "Utbetaling: [UTBETALINGSDATO]\n"
                 "Andre ytelser: [YTELSER]"),
                
                ("7. FERIE",
                 "Ferie reguleres av ferieloven\n"
                 "Feriepenger utbetales [UTBETALINGSTIDSPUNKT]"),
                
                ("8. OPPSIGELSE",
                 "Oppsigelsestid i henhold til arbeidsmiljøloven\n"
                 "Ved oppsigelse fra arbeidsgiver: [OPPSIGELSESTID]\n"
                 "Ved oppsigelse fra arbeidstaker: [OPPSIGELSESTID]")
            ]
            
            for title, content in sections:
                # Section header
                layout.add(Paragraph(
                    title,
                    font="Helvetica-Bold",
                    font_size=Decimal(12),
                    font_color=primary_color,
                    margin_top=Decimal(15),
                    margin_bottom=Decimal(5)
                ))
                
                # Section content
                layout.add(Paragraph(
                    content,
                    font="Helvetica",
                    font_size=Decimal(10),
                    font_color=text_color,
                    margin_bottom=Decimal(10)
                ))
        
        elif page_num == 1:
            # Second page - Additional terms and signature
            layout.add(Paragraph(
                "TILLEGGSBESTEMMELSER",
                font="Helvetica-Bold",
                font_size=Decimal(14),
                font_color=primary_color,
                margin_top=Decimal(20),
                margin_bottom=Decimal(15)
            ))
            
            additional_sections = [
                ("9. TAUSHETSPLIKT",
                 "Arbeidstaker har taushetsplikt om bedriftens forhold"),
                
                ("10. KONKURRANSEKLAUSUL",
                 "Eventuelle konkurranseklausuler [DETALJER]"),
                
                ("11. ØVRIGE BESTEMMELSER",
                 "Andre relevante bestemmelser for arbeidsforholdet")
            ]
            
            for title, content in additional_sections:
                layout.add(Paragraph(
                    title,
                    font="Helvetica-Bold",
                    font_size=Decimal(12),
                    font_color=primary_color,
                    margin_top=Decimal(15),
                    margin_bottom=Decimal(5)
                ))
                
                layout.add(Paragraph(
                    content,
                    font="Helvetica",
                    font_size=Decimal(10),
                    font_color=text_color,
                    margin_bottom=Decimal(10)
                ))
            
            # Signature section
            layout.add(Paragraph(
                "UNDERSKRIFT",
                font="Helvetica-Bold",
                font_size=Decimal(14),
                font_color=primary_color,
                margin_top=Decimal(40),
                margin_bottom=Decimal(20)
            ))
            
            # Create signature table
            signature_table = FlexibleColumnWidthTable(
                number_of_columns=2,
                number_of_rows=4
            )
            
            # Table headers
            signature_table.add(TableCell(Paragraph("ARBEIDSGIVER", font="Helvetica-Bold", font_size=Decimal(10))))
            signature_table.add(TableCell(Paragraph("ARBEIDSTAKER", font="Helvetica-Bold", font_size=Decimal(10))))
            
            # Date fields
            signature_table.add(TableCell(Paragraph("Dato: ________________", font="Helvetica", font_size=Decimal(10))))
            signature_table.add(TableCell(Paragraph("Dato: ________________", font="Helvetica", font_size=Decimal(10))))
            
            # Signature fields
            signature_table.add(TableCell(Paragraph("Underskrift: ________________", font="Helvetica", font_size=Decimal(10))))
            signature_table.add(TableCell(Paragraph("Underskrift: ________________", font="Helvetica", font_size=Decimal(10))))
            
            # Name fields
            signature_table.add(TableCell(Paragraph("Navn: ________________", font="Helvetica", font_size=Decimal(10))))
            signature_table.add(TableCell(Paragraph("Navn: ________________", font="Helvetica", font_size=Decimal(10))))
            
            layout.add(signature_table)
    
    # Save the recreated document
    output_path = "recreated_norwegian_contract.pdf"
    with open(output_path, "wb") as pdf_file:
        PDF.dumps(pdf_file, document)
    
    print(f"Norwegian employment contract recreated: {output_path}")
    return output_path

if __name__ == "__main__":
    recreate_norwegian_employment_contract()
