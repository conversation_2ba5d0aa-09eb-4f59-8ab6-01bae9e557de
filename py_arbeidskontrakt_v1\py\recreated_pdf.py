"""
Generated PDF Recreation Code
Source: corrected_norwegian_contract.pdf
Generated by PDF Analyzer
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

from pdf_engine import PDFEngine


def recreate_pdf():
    """Recreate the analyzed PDF using PDF Engine."""
    
    # Initialize the PDF engine
    engine = PDFEngine()
    
    # Create interactive form
    form = engine.create_form("recreated_form.pdf")
    
    form.add_text_field("virksomhetens_navn", x=43.8, y=701.2, width=505.3, height=15.9, default_value="Ringerike Landskap AS")
    form.add_text_field("organisasjonsnummer", x=43.8, y=672.3, width=505.3, height=15.9, default_value="123 456 789")
    form.add_text_field("arbeidsgiver_adresse", x=43.8, y=644.2, width=505.3, height=15.9, default_value="Hovedgata 123, 3500 Hønefoss")
    form.add_text_field("arbeidstaker_navn", x=43.8, y=591.4, width=364.8, height=15.9, default_value="")
    form.add_text_field("fodselsdato", x=414.0, y=591.4, width=137.1, height=15.9, default_value="")
    form.add_text_field("arbeidstaker_adresse", x=43.8, y=563.2, width=505.3, height=15.9, default_value="")
    form.add_text_field("arbeidsplass_adresse", x=43.8, y=510.7, width=505.3, height=15.9, default_value="")
    form.add_text_field("ansatt_som", x=43.8, y=438.8, width=505.3, height=36.2, default_value="")
    form.add_text_field("arbeidstid_per_uke", x=43.8, y=300.0, width=100.0, height=15.9, default_value="")
    
    # Render the form
    form.render_to_file("recreated_form.pdf")
    print("Interactive form created: recreated_form.pdf")


if __name__ == "__main__":
    recreate_pdf()